(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6e6e166a"],{"16f4":function(t,e,n){t.exports=n.p+"static/img/404.d351eeaa.png"},"8cdb":function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"wscn-http404-container"},[n("div",{staticClass:"wscn-http404"},[n("div",{staticClass:"bullshit"},[n("p",{staticStyle:{color:"#20232a","font-weight":"600","font-size":"30px","font-family":"PingFangSC, PingFangSC-Semibold","line-height":"42px"}},[t._v("很抱歉，您的页面迷路了~")]),t._v(" "),n("el-button",{staticStyle:{width:"132px",background:"#E15536","border-radius":"4px","font-weight":"400","font-size":"14px","margin-top":"10px",color:"#fff",border:"none"},on:{click:function(e){return t.$router.push("/")}}},[t._v("返回首页")])],1),t._v(" "),t._m(0)])])},a=[function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"pic-404"},[i("img",{staticStyle:{width:"390px",height:"386px"},attrs:{src:n("16f4"),alt:"404"}})])}],s={name:"Page404",computed:{message:function(){return"The webmaster said that you can not enter this page..."}}},c=s,o=(n("c842"),n("2877")),r=Object(o["a"])(c,i,a,!1,null,"38b2042e",null);e["default"]=r.exports},b612:function(t,e,n){},c842:function(t,e,n){"use strict";var i=n("b612"),a=n.n(i);a.a}}]);