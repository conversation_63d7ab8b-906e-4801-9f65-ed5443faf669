<template>
  <div
    class="sidebar-logo-container"
    :class="{ collapse: collapse }"
  >
    <img
      :src="logo"
      class="sidebar-logo"
    />
  </div>
</template>

<script>
export default {
  name: 'SidebarLogo',
  props: {
    collapse: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
      title: '神领TMS管理系统'
    }
  },
  computed: {
    logo() {
      return require('../../../assets/logo.png')
    }
  }
}
</script>

<style lang="scss" scoped>
.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  position: relative;
  width: 100%;
  height: 120px;
  line-height: 70px;
  // background-image: linear-gradient(to right, #d72228, #ed9247);
  text-align: center;
  overflow: hidden;
  padding: 20px 20px 0;
  margin-bottom: 15px;
  .sidebar-logo {
    width: 152px;
    height: 113px;
  }
  & .sidebar-logo-link {
    height: 100%;
    width: 100%;

    & .sidebar-logo {
      width: 32px;
      height: 32px;
      vertical-align: middle;
      margin-right: 12px;
    }

    & .sidebar-title {
      display: inline-block;
      margin: 0;
      color: #fff;
      font-weight: 600;
      line-height: 50px;
      font-size: 14px;
      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
      vertical-align: middle;
    }
  }

  &.collapse {
    .sidebar-logo {
      margin-right: 0px;
    }
  }
}
</style>
