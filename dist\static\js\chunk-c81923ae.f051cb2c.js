(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-c81923ae"],{"00ac":function(t,a,e){},1089:function(t,a,e){"use strict";var r=e("56c3"),n=e.n(r);n.a},"12db":function(t,a,e){"use strict";e.r(a);var r=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{attrs:{id:"order-account-chart"}})},n=[],s=(e("7f7f"),e("313e")),i=e.n(s),o=(e("d015"),{name:"OrderAccountChart",props:{orderAccountChartY:{type:Array,required:!0},orderAccountChartX:{type:Array,required:!0}},data:function(){return{}},methods:{initial:function(){var t=this._props,a=t.orderAccountChartX,e=t.orderAccountChartY,r=i.a.init(document.getElementById("order-account-chart")),n={tooltip:{borderColor:"#EBEEF5",borderWidth:1,padding:[5,16,5,14],trigger:"axis",backgroundColor:"rgba(255,255,255,0.96)",formatter:function(t){return'<span style="color:#818693;font-size:12px;margin-right:17px;margin-bottom:4px;display:inline-block">日期：</span><span style="color:#20232A;font-size:12px;display:inline-block">'+t[0].name+'</span><br /><span style="color:#818693;font-size:12px;margin-bottom:4px;display:inline-block">订单总量：</span><span style="color:#20232A;font-size:12px;display:inline-block;float:right">'+t[0].data+"笔</span>"}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",boundaryGap:!0,data:e,axisLabel:{show:!0,interval:0,textStyle:{color:"#20232A",fontStyle:"normal",fontFamily:"微软雅黑",fontSize:12},margin:17},axisTick:{show:!1},axisLine:{lineStyle:{color:"#E5E9ED"}},splitLine:{show:!1,lineStyle:{color:"#E5E9ED"}}},yAxis:[{type:"value",splitNumber:5,axisLabel:{textStyle:{color:"#a8aab0",fontStyle:"normal",fontFamily:"微软雅黑",fontSize:12}},axisLine:{show:!1},axisTick:{show:!1},splitLine:{show:!0,lineStyle:{color:"#E5E9ED"}}}],series:[{symbol:"circle",symbolSize:6,name:"2019",type:"line",itemStyle:{normal:{color:"#E25433",lineStyle:{color:"#E25433",width:1},areaStyle:{color:new i.a.graphic.LinearGradient(0,1,0,0,[{offset:0,color:"rgba(225,85,54,0.21)"},{offset:1,color:"rgba(225,85,54,0.00)"}])}}},data:a}]};r.setOption(n),window.addEventListener("resize",(function(){r.resize()}))}}}),l=o,c=(e("2351"),e("2877")),u=Object(c["a"])(l,r,n,!1,null,"3c7d3a40",null);a["default"]=u.exports},"19e5":function(t,a,e){"use strict";e.r(a);var r=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticStyle:{height:"208px",margin:"0 auto"},attrs:{id:t.id}})},n=[],s=(e("ac6a"),e("c5f6"),e("313e")),i=e.n(s),o=(e("d015"),{name:"BallChart",props:{data:{type:Array,required:!0},id:{type:String,required:!0},xDistance:{type:Number,required:!0}},methods:{initial:function(){var t=this._props,a=t.data,e=t.id,r=t.xDistance,n=i.a.init(document.getElementById(e)),s=[];a.forEach((function(t,a){s.push({type:"liquidFill",radius:"60%",center:["".concat(r*(a+.5),"%"),"45%"],data:[{value:1,itemStyle:{color:"transparent"}},t.value/100],backgroundStyle:{color:[t.otherColor]},color:[t.color],itemStyle:{shadowColor:"transparent"},outline:{borderDistance:5,itemStyle:{borderWidth:1,borderColor:t.color}},label:{position:["50%","50%"],formatter:function(){return"".concat(t.value,"%")},fontSize:16,color:[t.labelColor],insideColor:[t.labelColor],fontWeight:"normal"}})}));var o={series:s,backgroundColor:"white"};n.setOption(o),window.addEventListener("resize",(function(){n.resize()}))}}}),l=o,c=(e("bddc"),e("2877")),u=Object(c["a"])(l,r,n,!1,null,"1b3cde6e",null);a["default"]=u.exports},2351:function(t,a,e){"use strict";var r=e("3f07"),n=e.n(r);n.a},2934:function(t,a,e){"use strict";e.d(a,"e",(function(){return n})),e.d(a,"f",(function(){return s})),e.d(a,"d",(function(){return i})),e.d(a,"a",(function(){return o})),e.d(a,"b",(function(){return l}));var r=e("b775"),n=function(t){return Object(r["a"])("/web-manager/common/transportLineType/simple","get",t)},s=function(t){return Object(r["a"])("/truckType/simple","get",t)},i=function(t){return Object(r["a"])("/web-manager/common/fleet/simple","get",t)},o=function(t){return Object(r["a"])("/areas/children","get",t)},l=function(t){return Object(r["a"])("/workspace","get",t)}},"3f07":function(t,a,e){},"4f28":function(t,a){t.exports="data:image/png;base64,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"},"55c4":function(t,a,e){t.exports=e.p+"static/img/useFeature6.67f8db98.png"},"56c3":function(t,a,e){},6891:function(t,a){t.exports="data:image/png;base64,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"},7274:function(t,a,e){"use strict";var r=e("00ac"),n=e.n(r);n.a},8365:function(t,a,e){t.exports=e.p+"static/img/useFeature5.cc8a9bb1.png"},"8e22":function(t,a,e){"use strict";e.d(a,"f",(function(){return r})),e.d(a,"a",(function(){return n})),e.d(a,"g",(function(){return s})),e.d(a,"e",(function(){return i})),e.d(a,"d",(function(){return o})),e.d(a,"b",(function(){return l})),e.d(a,"c",(function(){return c}));var r=[{value:0,label:"运输中",url:"/business/order-manage?status=",status:23005},{value:0,label:"派送中",url:"/business/order-manage?status=",status:23008}],n=[{value:0,label:"待取件",url:"/business/order-manage?status=",status:23e3},{value:0,label:"待派件",url:"/business/order-manage?status=",status:23007},{value:0,label:"未分配运输",url:"/transport/transport-task?status=",status:1},{value:0,label:"超时运输",url:"/transport/transport-task?status=",status:5}],s=[{url:e("6891"),label:"快递作业",link:"/branches/operational"},{url:e("dc02"),label:"运输任务",link:"/transport/transport-task"},{url:e("4f28"),label:"线路管理",link:"/transport/line-manage"},{url:e("a5bb"),label:"车辆管理",link:"/transit/vehicle"},{url:e("8365"),label:"司机管理",link:"/transit/driver"},{url:e("55c4"),label:"运费查询",link:"/transit/freight-manage"}],i=[{name:"广东",value:2500},{name:"山东",value:2400},{name:"江苏",value:2200},{name:"浙江",value:2100},{name:"河南",value:1900},{name:"四川",value:1700},{name:"湖北",value:1600},{name:"台湾",value:1400},{name:"福建",value:1300},{name:"湖南",value:1200},{name:"上海",value:1050},{name:"安徽",value:950},{name:"河北",value:800},{name:"北京",value:700},{name:"陕西",value:600},{name:"江西",value:550},{name:"重庆",value:450},{name:"辽宁",value:400},{name:"云南",value:390},{name:"广西",value:380},{name:"香港",value:370},{name:"山西",value:350},{name:"内蒙古",value:320},{name:"贵州",value:300},{name:"新疆",value:280},{name:"天津",value:260},{name:"黑龙江",value:240},{name:"吉林",value:220},{name:"甘肃",value:200},{name:"海南",value:180},{name:"宁夏",value:160},{name:"青海",value:140},{name:"西藏",value:120},{name:"澳门",value:100}],o=[{toName:"西安",fromName:"乌鲁木齐",value:100,name:{lineNumber:"XL877654",lineName:"乌鲁木齐直达西安",lineType:"接驳路线",fromAgency:"乌鲁木齐分拣中心",toAgency:"西安分拣中心",distance:"2561千米",cost:"8368元",time:"1678分钟"}},{toName:"成都",fromName:"拉萨",value:100,name:{lineNumber:"XL877655",lineName:"拉萨直达成都",lineType:"接驳路线",fromAgency:"拉萨分拣中心",toAgency:"成都分拣中心",distance:"1991千米",cost:"8368元",time:"1678分钟"}},{toName:"北京",fromName:"西安",value:100,name:{lineNumber:"XL877655",lineName:"西安直达北京",lineType:"接驳路线",fromAgency:"西安分拣中心",toAgency:"北京分拣中心",distance:"1075千米",cost:"8368元",time:"1678分钟"}},{toName:"哈尔滨",fromName:"北京",value:100,name:{lineNumber:"XL877655",lineName:"北京直达哈尔滨",lineType:"接驳路线",fromAgency:"北京分拣中心",toAgency:"哈尔滨分拣中心",distance:"1233千米",cost:"8368元",time:"1678分钟"}},{toName:"西安",fromName:"成都",value:100,name:{lineNumber:"XL877655",lineName:"成都直达西安",lineType:"接驳路线",fromAgency:"成都分拣中心",toAgency:"西安分拣中心",distance:"797千米",cost:"8368元",time:"1678分钟"}},{toName:"西安",fromName:"海口",value:100,name:{lineNumber:"XL877655",lineName:"海口直达西安",lineType:"接驳路线",fromAgency:"海口分拣中心",toAgency:"西安分拣中心",distance:"2069千米",cost:"8368元",time:"1678分钟"}},{toName:"北京",fromName:"海口",value:100,name:{lineNumber:"XL877655",lineName:"海口直达北京",lineType:"接驳路线",fromAgency:"海口分拣中心",toAgency:"北京分拣中心",distance:"2598千米",cost:"8368元",time:"1678分钟"}},{toName:"上海",fromName:"海口",value:100,name:{lineNumber:"XL877655",lineName:"海口直达上海",lineType:"接驳路线",fromAgency:"海口分拣中心",toAgency:"上海分拣中心",distance:"1987千米",cost:"8368元",time:"1678分钟"}},{toName:"上海",fromName:"北京",value:100,name:{lineNumber:"XL877655",lineName:"北京直达上海",lineType:"接驳路线",fromAgency:"北京分拣中心",toAgency:"上海分拣中心",distance:"1210千米",cost:"8368元",time:"1678分钟"}},{toName:"台北",fromName:"上海",value:100,name:{lineNumber:"XL877655",lineName:"上海直达台北",lineType:"接驳路线",fromAgency:"上海分拣中心",toAgency:"台北分拣中心",distance:"1210千米",cost:"8368元",time:"1678分钟"}}],l={"上海":[121.4648,31.2891],"东莞":[113.8953,22.901],"东营":[118.7073,37.5513],"中山":[113.4229,22.478],"临汾":[111.4783,36.1615],"临沂":[118.3118,35.2936],"丹东":[124.541,40.4242],"丽水":[119.5642,28.1854],"乌鲁木齐":[87.9236,43.5883],"佛山":[112.8955,23.1097],"保定":[115.0488,39.0948],"兰州":[103.5901,36.3043],"包头":[110.3467,41.4899],"北京":[116.4551,40.2539],"北海":[109.314,21.6211],"南京":[118.8062,31.9208],"南宁":[108.479,23.1152],"南昌":[116.0046,28.6633],"南通":[121.1023,32.1625],"厦门":[118.1689,24.6478],"台州":[121.1353,28.6688],"合肥":[117.29,32.0581],"呼和浩特":[111.4124,40.4901],"咸阳":[108.4131,34.8706],"哈尔滨":[127.9688,45.368],"唐山":[118.4766,39.6826],"嘉兴":[120.9155,30.6354],"大同":[113.7854,39.8035],"大连":[122.2229,39.4409],"天津":[117.4219,39.4189],"太原":[112.3352,37.9413],"威海":[121.9482,37.1393],"宁波":[121.5967,29.6466],"宝鸡":[107.1826,34.3433],"宿迁":[118.5535,33.7775],"常州":[119.4543,31.5582],"广州":[113.5107,23.2196],"廊坊":[116.521,39.0509],"延安":[109.1052,36.4252],"张家口":[115.1477,40.8527],"徐州":[117.5208,34.3268],"德州":[116.6858,37.2107],"惠州":[114.6204,23.1647],"成都":[103.9526,30.7617],"扬州":[119.4653,32.8162],"承德":[117.5757,41.4075],"拉萨":[91.1865,30.1465],"无锡":[120.3442,31.5527],"日照":[119.2786,35.5023],"昆明":[102.9199,25.4663],"杭州":[119.5313,29.8773],"枣庄":[117.323,34.8926],"柳州":[109.3799,24.9774],"株洲":[113.5327,27.0319],"武汉":[114.3896,30.6628],"汕头":[117.1692,23.3405],"江门":[112.6318,22.1484],"沈阳":[123.1238,42.1216],"沧州":[116.8286,38.2104],"河源":[114.917,23.9722],"泉州":[118.3228,25.1147],"泰安":[117.0264,36.0516],"泰州":[120.0586,32.5525],"济南":[117.1582,36.8701],"济宁":[116.8286,35.3375],"海口":[110.3893,19.8516],"淄博":[118.0371,36.6064],"淮安":[118.927,33.4039],"深圳":[114.5435,22.5439],"清远":[112.9175,24.3292],"温州":[120.498,27.8119],"渭南":[109.7864,35.0299],"湖州":[119.8608,30.7782],"湘潭":[112.5439,27.7075],"滨州":[117.8174,37.4963],"潍坊":[119.0918,36.524],"烟台":[120.7397,37.5128],"玉溪":[101.9312,23.8898],"珠海":[113.7305,22.1155],"盐城":[120.2234,33.5577],"盘锦":[121.9482,41.0449],"石家庄":[114.4995,38.1006],"福州":[119.4543,25.9222],"秦皇岛":[119.2126,40.0232],"绍兴":[120.564,29.7565],"聊城":[115.9167,36.4032],"肇庆":[112.1265,23.5822],"舟山":[122.2559,30.2234],"苏州":[120.6519,31.3989],"莱芜":[117.6526,36.2714],"菏泽":[115.6201,35.2057],"营口":[122.4316,40.4297],"葫芦岛":[120.1575,40.578],"衡水":[115.8838,37.7161],"衢州":[118.6853,28.8666],"西宁":[101.4038,36.8207],"西安":[109.1162,34.2004],"贵阳":[106.6992,26.7682],"连云港":[119.1248,34.552],"邢台":[114.8071,37.2821],"邯郸":[114.4775,36.535],"郑州":[113.4668,34.6234],"鄂尔多斯":[108.9734,39.2487],"重庆":[107.7539,30.1904],"金华":[120.0037,29.1028],"铜川":[109.0393,35.1947],"银川":[106.3586,38.1775],"镇江":[119.4763,31.9702],"长春":[125.8154,44.2584],"长沙":[113.0823,28.2568],"长治":[112.8625,36.4746],"阳泉":[113.4778,38.0951],"青岛":[120.4651,36.3373],"韶关":[113.7964,24.7028],"台北":[121.5,25.05]},c=[{name:"南海诸岛",value:0,itemStyle:{normal:{opacity:0,label:{show:!1}}}},{name:"北京",itemStyle:{normal:{areaColor:"#FFE4B5"}}},{name:"天津",itemStyle:{normal:{areaColor:"#ECDC7E"}}},{name:"上海",itemStyle:{normal:{areaColor:"#ECDC7E"}}},{name:"重庆",itemStyle:{normal:{areaColor:"#FFE4B5"}}},{name:"河北",itemStyle:{normal:{areaColor:"#FF9739"}}},{name:"河南",itemStyle:{normal:{areaColor:"#FFE4B5"}}},{name:"云南",itemStyle:{normal:{areaColor:"#FFC257"}}},{name:"辽宁",itemStyle:{normal:{areaColor:"#ECDC7E"}}},{name:"黑龙江",itemStyle:{normal:{areaColor:"#ECDC7E"}}},{name:"湖南",itemStyle:{normal:{areaColor:"#FFC257"}}},{name:"安徽",itemStyle:{normal:{areaColor:"#FFC257"}}},{name:"山东",itemStyle:{normal:{areaColor:"#ECDC7E"}}},{name:"新疆",itemStyle:{normal:{areaColor:"#ECDC7E"}}},{name:"江苏",itemStyle:{normal:{areaColor:"#FF9739"}}},{name:"浙江",itemStyle:{normal:{areaColor:"#FFE4B5"}}},{name:"江西",itemStyle:{normal:{areaColor:"#ECDC7E"}}},{name:"湖北",itemStyle:{normal:{areaColor:"#FF9739"}}},{name:"广西",itemStyle:{normal:{areaColor:"#ECDC7E"}}},{name:"甘肃",itemStyle:{normal:{areaColor:"#FF9739"}}},{name:"山西",itemStyle:{normal:{areaColor:"#ECDC7E"}}},{name:"内蒙古",itemStyle:{normal:{areaColor:"#FFE4B5"}}},{name:"陕西",itemStyle:{normal:{areaColor:"#FFC257"}}},{name:"吉林",itemStyle:{normal:{areaColor:"#FF9739"}}},{name:"福建",itemStyle:{normal:{areaColor:"#FF9739"}}},{name:"贵州",itemStyle:{normal:{areaColor:"#FF9739"}}},{name:"广东",itemStyle:{normal:{areaColor:"#FFE4B5"}}},{name:"青海",itemStyle:{normal:{areaColor:"#FFC257"}}},{name:"西藏",itemStyle:{normal:{areaColor:"#FFE4B5"}}},{name:"四川",itemStyle:{normal:{areaColor:"#ECDC7E"}}},{name:"宁夏",itemStyle:{normal:{areaColor:"#ECDC7E"}}},{name:"海南",itemStyle:{normal:{areaColor:"#FFC257"}}},{name:"台湾",itemStyle:{normal:{areaColor:"#FFC257"}}},{name:"香港",itemStyle:{normal:{areaColor:"#dc9bbb"}}},{name:"澳门",itemStyle:{normal:{areaColor:"#e0f7cc"}}}]},9406:function(t,a,e){"use strict";e.r(a);var r=function(){var t=this,a=this,r=a.$createElement,n=a._self._c||r;return n("div",{staticClass:"dashboard-container"},[n("el-row",{staticClass:"dashboard-row",attrs:{gutter:24,justify:"center"}},[n("el-col",{staticClass:"dashboard-col",attrs:{span:14}},[n("el-card",{staticClass:"box-card institution"},[n("p",{staticClass:"institutionalOverview"},[a._v("机构概述")]),a._v(" "),n("div",{staticClass:"content"},[n("div",{staticClass:"left-content"},[n("div",{staticClass:"name"},[a._v(a._s(a.organOverview.organName))]),a._v(" "),n("div",{staticClass:"address"},[a._v("地址："+a._s(a.organOverview.organAddress))]),a._v(" "),n("div",{staticClass:"duty-people"},[a._v("负责人："+a._s(a.organOverview.principal)+" "+a._s(a.organOverview.phone))]),a._v(" "),n("div",{staticClass:"search-sales-department el-button el-button--warning is-plain",on:{click:function(t){return a.showDepartment()}}},[a._v("\n              查看营业部分布\n            ")])]),a._v(" "),n("div",{staticClass:"right-content"},[n("div",{staticClass:"item"},[n("div",[n("div",{staticClass:"label"},[a._v("分拣中心(个)")]),a._v(" "),n("div",{staticClass:"num",on:{click:function(){t.$router.push({path:"/branches/organization-manage"})}}},[a._v(a._s(a.organOverview.sortingCenterNumber))])])]),a._v(" "),n("div",{staticClass:"item"},[n("div",[n("div",{staticClass:"label"},[a._v("营业部(个)")]),a._v(" "),n("div",{staticClass:"num",on:{click:function(){t.$router.push({path:"/branches/organization-manage"})}}},[a._v(a._s(a.organOverview.agencyNumber))])])]),a._v(" "),n("div",{staticClass:"item"},[n("div",[n("div",{staticClass:"label"},[a._v("司机人数(个)")]),a._v(" "),n("div",{staticClass:"num",on:{click:function(){t.$router.push({path:"/transit/driver"})}}},[a._v(a._s(a.organOverview.driverNumber))])])]),a._v(" "),n("div",{staticClass:"item"},[n("div",[n("div",{staticClass:"label"},[a._v("快递员人数(个)")]),a._v(" "),n("div",{staticClass:"num",on:{click:function(){t.$router.push({path:"/branches/operational-range"})}}},[a._v(a._s(a.organOverview.courierNumber))])])])])])])],1),a._v(" "),n("el-col",{staticClass:"dashboard-col2",attrs:{span:10}},[n("el-card",{staticClass:"box-card right-info",attrs:{"body-style":{paddingTop:"21px",minHeight:"60px"}}},[n("div",{staticClass:"header"},[n("p",[a._v("今日数据")]),a._v(" "),n("div",{staticClass:"refresh-box",on:{click:function(t){return a.handleRefreshTodayDataTime()}}},[a._v("\n            "+a._s(a.todayDataTime)+"\n          ")])]),a._v(" "),n("el-row",{staticClass:"row-bg",attrs:{span:24,type:"flex",justify:"space-around"}},[n("el-col",{attrs:{span:8}},[n("div",{staticClass:"label"},[a._v("订单金额(元)")]),a._v(" "),n("div",{staticClass:"value",attrs:{id:"my-number1"}},[a._v(a._s(a.todayData.orderAmount))]),a._v(" "),n("div",{staticClass:"bottom",class:a.todayData.orderAmountChanges<=0?"active":""},[a._v("较昨日 "+a._s(a.todayData.orderAmountChanges<=0?"-":"+")+a._s(a.todayData.orderAmountChanges))])]),a._v(" "),n("el-col",{attrs:{span:8}},[n("div",{staticClass:"label"},[a._v("订单数量(笔)")]),a._v(" "),n("div",{staticClass:"value",attrs:{id:"my-number2"}},[a._v(a._s(a.todayData.orderNumber))]),a._v(" "),n("div",{staticClass:"bottom",class:a.todayData.orderNumberChanges<=0?"active":""},[a._v("较昨日 "+a._s(a.todayData.orderNumberChanges<=0?"-":"+")+a._s(a.todayData.orderNumberChanges))])]),a._v(" "),n("el-col",{attrs:{span:8}},[n("div",{staticClass:"label"},[a._v("运输任务(次)")]),a._v(" "),n("div",{staticClass:"value",attrs:{id:"my-number3"}},[a._v(a._s(a.todayData.transportTaskNumber))]),a._v(" "),n("div",{staticClass:"bottom",class:a.todayData.transportTaskNumberChanges<=0?"active":""},[a._v("较昨日 "+a._s(a.todayData.transportTaskNumberChanges<=0?"-":"+")+a._s(a.todayData.transportTaskNumberChanges))])])],1)],1)],1)],1),a._v(" "),n("el-row",{staticClass:"dashboard-row2",attrs:{gutter:24}},[n("el-col",{staticClass:"dashboard-col3",attrs:{span:14}},[n("el-card",{staticClass:"hots cover-card backlog notTasking"},[n("div",{staticClass:"header"},[n("p",{staticClass:"todoTasks"},[a._v("\n            待办任务\n            "),n("el-tooltip",{staticClass:"tooltip item",attrs:{effect:"light",placement:"bottom-start"}},[n("div",{staticClass:"todoTasks1",attrs:{slot:"content"},slot:"content"},[a._v("\n                待取件率=待取件/(下单数量-取消数量)，且取件类型=上门取件"),n("br"),a._v("\n                待派送率=待派送/(待派送+派送中+已签收+拒收)"),n("br"),a._v("\n                未分配率=未分配/全部数据"),n("br"),a._v("\n                超时率=超时任务/(已完成+进行中+已取消）"),n("br")]),a._v(" "),n("img",{attrs:{src:e("d6ed")}})])],1),a._v(" "),n("div",{staticClass:"refresh-box",on:{click:function(t){return a.handleRefreshNotTaskDataTime()}}},[a._v("\n            "+a._s(a.notTaskDataTime)+"\n          ")])]),a._v(" "),n("div",{staticClass:"ball-chart-box"},[n("BallChart",{ref:"ballChart",attrs:{id:"ballChart",data:a.backlogChartData,"x-distance":25}})],1),a._v(" "),n("div",{staticClass:"bottom-label"},a._l(a.backlogListdata,(function(t,e){return n("div",{key:e,staticClass:"item",on:{click:function(e){return a.handleBacklog(t.url,t.status)}}},[n("span",[a._v(a._s(t.label))]),a._v(" "),n("span",[a._v(a._s(t.value))])])})),0)])],1),a._v(" "),n("el-col",{staticClass:"dashboard-col4",attrs:{span:10}},[n("el-card",{staticClass:"hots cover-card backlog tasking"},[n("div",{staticClass:"header"},[n("p",{staticClass:"Tasksinprogress"},[a._v("\n            执行中任务\n            "),n("el-tooltip",{staticClass:"tooltip item",attrs:{effect:"light",placement:"bottom"}},[n("div",{staticClass:"toolipcontent",attrs:{slot:"content"},slot:"content"},[a._v("\n                运输率=运输中/(全部订单-待取件-已取件-网点入库-待装车-已取消）"),n("br"),a._v("\n                派送率=派送中/(待派送+派送中+已签收+拒收）"),n("br")]),a._v(" "),n("img",{attrs:{src:e("d6ed")}})])],1),a._v(" "),n("div",{staticClass:"refresh-box",on:{click:function(t){return a.handleRefreshTaskingDataTime()}}},[a._v("\n            "+a._s(a.taskingDataTime)+"\n          ")])]),a._v(" "),n("div",[n("BallChart",{ref:"ballChartIng",attrs:{id:"ballChartIng",data:a.taskingChartData,"x-distance":48}})],1),a._v(" "),n("div",{staticClass:"bottom-label"},a._l(a.taskingListData,(function(t,e){return n("div",{key:e,staticClass:"item",on:{click:function(e){return a.handleBacklog(t.url,t.status)}}},[n("span",[a._v(a._s(t.label))]),a._v(" "),n("span",[a._v(a._s(t.value))])])})),0)])],1)],1),a._v(" "),n("el-row",{staticClass:"dashboard-row2",staticStyle:{"margin-bottom":"20px"},attrs:{gutter:20}},[n("el-col",{staticClass:"dashboard-col5",attrs:{xs:24,sm:24}},[n("el-card",{staticClass:"hots cover-card useFeature"},[n("p",{staticClass:"Commonfunctions"},[a._v("常用功能")]),a._v(" "),n("div",{staticClass:"useFeaturelist"},a._l(a.useFeatureList,(function(t,e){return n("div",{key:e,staticClass:"item",on:{click:function(e){return a.handleClick(t.link)}}},[n("img",{attrs:{src:t.url}}),a._v(" "),n("p",[a._v(a._s(t.label))])])})),0)])],1)],1),a._v(" "),n("el-row",{staticClass:"dashboard-row2",attrs:{gutter:24}},[n("el-col",{attrs:{span:12}},[n("el-card",{staticClass:"hots cover-card lineManage"},[n("p",{staticClass:"LineManagement"},[a._v("线路管理")]),a._v(" "),n("LineManageChart",{ref:"lineManageChart"})],1)],1),a._v(" "),n("el-col",{staticClass:"dashboard-col4",attrs:{span:12}},[n("el-card",{staticClass:"hots cover-card transport-task"},[n("div",{staticClass:"header"},[n("p",[a._v("\n            运输任务\n          ")]),a._v(" "),n("div",{staticClass:"search-more",on:{click:a.handleToTransportTask}},[a._v("\n            查看更多\n          ")])]),a._v(" "),n("div",{staticClass:"table_body"},[n("div",{staticClass:"table_th"},[n("div",{staticClass:"tr1 th_style"},[a._v("任务编号")]),a._v(" "),n("div",{staticClass:"tr2 th_style"},[a._v("起始地")]),a._v(" "),n("div",{staticClass:"tr3 th_style"},[a._v("目的地")]),a._v(" "),n("div",{staticClass:"tr4 th_style"},[a._v("车辆")]),a._v(" "),n("div",{staticClass:"tr4 th_style"},[a._v("任务状态")])]),a._v(" "),n("div",{staticClass:"table_main_body",on:{mousemove:function(t){return a.handleStopScroll()},mouseleave:function(t){return a.handleStartScroll()}}},[n("div",{staticClass:"table_inner_body",class:a.tableTop?"startTransition":"",style:{top:a.tableTop+"px"}},a._l(a.transportTaskListData,(function(t,e){return n("div",{key:e,staticClass:"table_tr"},[n("div",{staticClass:"tr1 tr"},[a._v(a._s(t.id))]),a._v(" "),n("div",{staticClass:"tr2 tr"},[a._v(a._s(t.startAgency))]),a._v(" "),n("div",{staticClass:"tr3 tr"},[a._v(" "+a._s(t.endAgency))]),a._v(" "),n("div",{staticClass:"tr4 tr"},[a._v(a._s(t.licensePlate))]),a._v(" "),n("div",{staticClass:"tr4 tr "},[a._v(a._s("1"==t.status?"待执行":"2"==t.status?"进行中":"3"==t.status?"待确认":"4"==t.status?"已完成":"已取消"))])])})),0)])])])],1)],1),a._v(" "),n("el-row",{staticClass:"dashboard-row2",attrs:{gutter:24}},[n("el-col",{staticClass:"dashboard-col3",attrs:{span:12}},[n("el-card",{staticClass:"hots cover-card orderAccount"},[n("div",{staticClass:"header"},[n("p",{staticClass:"Totalorders"},[a._v("\n            订单总量\n          ")]),a._v(" "),n("div",{staticClass:"Totalorderstime"},[a._v("\n            "+a._s(a.getMonthsBeforeSix())+"\n          ")])]),a._v(" "),n("p",{staticClass:"Totalorderstype"},[a._v("单位：笔")]),a._v(" "),n("div",{ref:"orderStatic",staticClass:"order-static"},[n("div",{staticClass:"item"},[n("div",{staticClass:"num",attrs:{id:"my-number4"}},[a._v(a._s(a.orderAccountTitleData.orderMaxNumber))]),a._v(" "),n("div",{staticClass:"label"},[a._v("订单最高值")])]),a._v(" "),n("div",{staticClass:"item"},[n("div",{staticClass:"num",attrs:{id:"my-number5"}},[a._v(a._s(a.orderAccountTitleData.orderAverageNumber))]),a._v(" "),n("div",{staticClass:"label"},[a._v("订单平均值")])]),a._v(" "),n("div",{staticClass:"item"},[n("div",{staticClass:"num",attrs:{id:"my-number6"}},[a._v(a._s(a.orderAccountTitleData.orderMinNumber))]),a._v(" "),n("div",{staticClass:"label"},[a._v("订单最低值")])])]),a._v(" "),n("OrderAccountChart",{ref:"orderAccountChart",attrs:{"order-account-chart-y":a.orderAccountChartY,"order-account-chart-x":a.orderAccountChartX}})],1)],1),a._v(" "),n("el-col",{staticClass:"dashboard-col4",attrs:{span:12}},[n("el-card",{staticClass:"hots cover-card orderDistribute"},[n("div",{staticClass:"header"},[n("p",{staticClass:"Orderdistribution"},[a._v("\n            订单分布\n          ")]),a._v(" "),n("div",{staticClass:"Orderdistributiontime"},[a._v("\n            "+a._s(a.getMonthsBeforeSix())+"\n          ")])]),a._v(" "),n("p",{staticClass:"Orderdistributiontype"},[a._v("单位：笔")]),a._v(" "),n("OrderDistributeChart",{ref:"orderDistributeChart"})],1)],1)],1),a._v(" "),n("el-dialog",{staticClass:"deparment",attrs:{visible:a.dialogVisible,width:"800px","before-close":a.handleClose},on:{"update:visible":function(t){a.dialogVisible=t}}},[n("div",{staticClass:"close-btn",on:{click:a.handleClose}}),a._v(" "),n("img",{attrs:{src:e("b5cd")}})]),a._v(" "),n("span",{staticClass:"gzt-remark"},[a._v("注：工作台展示数据为虚拟数据")])],1)},n=[],s=e("2934"),i=e("19e5"),o=e("c477"),l=e("12db"),c=e("c40d"),u=e("8e22"),m={name:"Dashboard",components:{BallChart:i["default"],LineManageChart:o["default"],OrderAccountChart:l["default"],OrderDistributeChart:c["default"]},data:function(){return{tableTimerInterval:2e3,tableTimer:null,tableTop:0,tableListSize:0,visibleSize:5,notTaskDataTime:"",taskingDataTime:"",todayDataTime:"",dialogVisible:!1,transportTaskListData:[],notChangeTransportTaskListData:[],useFeatureList:u["g"],backlogListdata:u["a"],backlogChartData:[{value:0},{value:0},{value:0},{value:0}],taskingChartData:[{value:0},{value:0}],taskingListData:u["f"],organOverview:{organName:"",organAddress:"",principal:"",phone:"",sortingCenterNumber:"",agencyNumber:"",driverNumber:"",courierNumber:""},todayData:{orderAmount:"",orderAmountChanges:"",orderNumber:"",orderNumberChanges:"",transportTaskNumber:"",transportTaskNumberChanges:""},backlog:{waitingPickupNumber:"",waitingPickupRate:"",waitingDispatchNumber:"",waitingDispatchRate:"",unassignedTransportTaskNumber:"",unassignedTransportTaskRate:"",timeoutTransportTaskNumber:"",timeoutTransportTaskRate:""},orderAccountTitleData:{orderMaxNumber:"",orderAverageNumber:"",orderMinNumber:""},orderAccountChartY:[],orderAccountChartX:[],isScrollNum:!0}},computed:{},mounted:function(){var t=this;this.todayDataTime=this.getCurrentTime(),this.taskingDataTime=this.getCurrentTime(),this.notTaskDataTime=this.getCurrentTime()+" - "+this.getAfterThreeDays(),this.getPageData(),document.querySelector(".dashboard-container").addEventListener("scroll",this.load),void 0!==document.hidden&&document.addEventListener("visibilitychange",(function(){document.hidden?clearInterval(t.tableTimer):t.tableActionFun()}))},methods:{load:function(){this.isScrollNum&&document.querySelector(".dashboard-container").scrollTop&&document.querySelector(".dashboard-container").scrollTop>600&&(this.isScrollNum=!1,this.addNumber(0,this.orderAccountTitleData.orderMaxNumber,"my-number4",300),this.addNumber(0,this.orderAccountTitleData.orderAverageNumber,"my-number5",300),this.addNumber(0,this.orderAccountTitleData.orderMinNumber,"my-number6",300))},tableActionFun:function(){this.tableListSize=this.transportTaskListData.length,this.transportTaskListData=this.notChangeTransportTaskListData.concat(this.notChangeTransportTaskListData),this.tableListSize>this.visibleSize&&(this.transportTaskListData=this.transportTaskListData.concat(this.transportTaskListData),this.tableTimerFun())},handleStopScroll:function(){clearInterval(this.tableTimer)},handleStartScroll:function(){this.tableActionFun()},tableTimerFun:function(){var t=this,a=0;this.tableTimer=setInterval((function(){a<t.transportTaskListData.length/2?(t.tableTop-=50,a++):(a=0,t.tableTop=0)}),this.tableTimerInterval)},handleClose:function(){this.dialogVisible=!1},showDepartment:function(){this.dialogVisible=!0},handleBacklog:function(t,a){this.$router.push({path:t+a})},getMonthsBeforeSix:function(){for(var t=new Date,a=t.getFullYear(),e=t.getMonth(),r=0,n=0,s=[],i=0;i<6;i++)e-i<1?(r=a-1,n=e-i+12>=10?e-i+12:"0"+(e-i+12),s.push(r+"-"+n)):(n=e-i>=10?e-i:"0"+(e-i),s.push(a+"-"+n));return s[5]+" - "+s[0]},getAfterThreeDays:function(){var t=new Date((new Date).getTime()+1728e5),a=t.getFullYear()+"-",e=(t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1)+"-",r=(t.getDate()<10?"0"+t.getDate():t.getDate())+" ";return a+e+r+"23:59"},handleRefreshNotTaskDataTime:function(){this.notTaskDataTime=String(this.getCurrentTime())+" - "+String(this.getAfterThreeDays())},handleRefreshTodayDataTime:function(){this.todayDataTime=this.getCurrentTime()},handleRefreshTaskingDataTime:function(){this.taskingDataTime=this.getCurrentTime()},getCurrentTime:function(){var t=new Date,a=t.getFullYear()+"-",e=(t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1)+"-",r=(t.getDate()<10?"0"+t.getDate():t.getDate())+" ",n=(t.getHours()<10?"0"+t.getHours():t.getHours())+":",s=t.getMinutes()<10?"0"+t.getMinutes():t.getMinutes();return a+e+r+n+s},getPageData:function(){var t=this;Object(s["b"])().then((function(a){t.organOverview=a.data.organOverview,t.todayData=a.data.todayData,t.addNumber(0,a.data.todayData.orderAmount,"my-number1",25),t.addNumber(0,a.data.todayData.orderNumber,"my-number2",2),t.addNumber(0,a.data.todayData.transportTaskNumber,"my-number3",1),t.dealWithBacklogChartData(a),t.dealWithTaskingListData(a),t.transportTaskListData=a.data.transportTaskList,t.notChangeTransportTaskListData=a.data.transportTaskList,t.orderAccountTitleData=a.data.orderLineChart,t.orderAccountChartY=a.data.orderLineChart.monthlyOrderList.map((function(t){return t.dateTime})),t.orderAccountChartX=a.data.orderLineChart.monthlyOrderList.map((function(t){return t.orderNumber})),t.$nextTick((function(){t.$refs.ballChart.initial(),t.$refs.ballChartIng.initial(),t.$refs.lineManageChart.initial(),t.$refs.orderAccountChart.initial(),t.$refs.orderDistributeChart.initial(),t.tableActionFun()}))}))},handleToTransportTask:function(){this.$router.push({path:"/transport/transport-task"})},handleClick:function(t){this.$router.push({path:t})},dealWithTaskingListData:function(t){var a=[t.data.todayData.taskInTransitNumber,t.data.todayData.taskInDeliveryNumber],e=["#E15536","#FFC257"],r=["#20232A","#20232A"],n=["#FFE5E0","#FFF1D9"];this.taskingListData=this.taskingListData.map((function(t,e){return Object.assign({},t,{value:a[e]})}));var s=[t.data.todayData.taskInTransitRate,t.data.todayData.taskInDeliveryRate];this.taskingChartData=this.taskingChartData.map((function(t,a){return{value:s[a],color:e[a],labelColor:r[a],otherColor:n[a]}}))},dealWithBacklogChartData:function(t){var a=[t.data.backlog.waitingPickupNumber,t.data.backlog.waitingDispatchNumber,t.data.backlog.unassignedTransportTaskNumber,t.data.backlog.timeoutTransportTaskNumber];this.backlogListdata=this.backlogListdata.map((function(t,e){return Object.assign({},t,{value:a[e]})}));var e=[t.data.backlog.waitingPickupRate,t.data.backlog.waitingDispatchRate,t.data.backlog.unassignedTransportTaskRate,t.data.backlog.timeoutTransportTaskRate],r=["#E15536","#FFC257","#FF9739","#ECDC7E"],n=["#20232A","#20232A","#20232A","#20232A"],s=["#FFE5E0","#FFF1D9","#FFEAD7","#FAFADE"];this.backlogChartData=this.backlogChartData.map((function(t,a){return{value:e[a],color:r[a],labelColor:n[a],otherColor:s[a]}}))},addNumber:function(t,a,e,r){var n,s=document.getElementById(e),i=t;i<a&&(n=setInterval((function(){i+=r,i>a?(clearInterval(n),s.innerHTML=a.toLocaleString(),i=0):s.innerHTML=i.toLocaleString()}),10))}}},d=m,v=(e("9c00"),e("2877")),b=Object(v["a"])(d,r,n,!1,null,"788e2305",null);a["default"]=b.exports},"9c00":function(t,a,e){"use strict";var r=e("ea1b"),n=e.n(r);n.a},a5bb:function(t,a){t.exports="data:image/png;base64,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"},b5cd:function(t,a,e){t.exports=e.p+"static/img/department_map.7a0c7499.png"},bddc:function(t,a,e){"use strict";var r=e("e555"),n=e.n(r);n.a},c40d:function(t,a,e){"use strict";e.r(a);var r=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{attrs:{id:"order-distribute-chart"}})},n=[],s=(e("7f7f"),e("55dd"),e("313e")),i=e.n(s),o=e("8e22"),l={name:"OrderAccountChart",data:function(){return{}},methods:{initial:function(){var t=i.a.init(document.getElementById("order-distribute-chart")),a=o["e"];a.sort((function(t,a){return t.value-a.value}));var e={tooltip:{borderColor:"#EBEEF5",borderWidth:1,padding:[5,16,5,14],trigger:"axis",backgroundColor:"rgba(255,255,255,0.96)",formatter:function(t){return'<span style="color:#818693;font-size:12px;margin-right:25px;margin-bottom:4px;display:inline-block">省份：</span><span style="color:#20232A;font-size:12px;display:inline-block">'+t[0].name+'</span><br /><span style="color:#818693;font-size:12px;margin-bottom:4px;display:inline-block">订单总量：</span><span style="color:#20232A;font-size:12px;display:inline-block;float:right">'+t[0].data+"笔</span>"}},xAxis:{type:"value",axisLine:{show:!1},axisTick:{show:!1},axisLabel:{margin:17},splitLine:{lineStyle:{color:"#EBEEF5"}}},grid:{left:"3%",right:"4%",bottom:"0%",height:"100%",containLabel:!0},yAxis:{type:"category",axisLabel:{interval:0,fontSize:11,color:"#20232A"},axisLine:{lineStyle:{color:"#EBEEF5"}},axisTick:{show:!1},data:a.map((function(t){return t.name}))},animationDurationUpdate:1e3,series:{type:"bar",id:"population",data:a.map((function(t){return t.value})),universalTransition:!0,barWidth:5,itemStyle:{color:"#E15536"}}};t.setOption(e),window.addEventListener("resize",(function(){t.resize()}))}}},c=l,u=(e("7274"),e("2877")),m=Object(u["a"])(c,r,n,!1,null,"ab5dec0a",null);a["default"]=m.exports},c477:function(t,a,e){"use strict";e.r(a);var r=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{attrs:{id:"line-manage-chart"}})},n=[],s=(e("7f7f"),e("313e")),i=e.n(s),o=(e("3139"),e("8e22")),l={name:"LineManageChart",data:function(){return{geoCoordMap:o["b"]}},methods:{convertData:function(t){for(var a=[],e=0;e<t.length;e++){var r=this.geoCoordMap[t[e].toName],n=this.geoCoordMap[t[e].fromName];r&&a.push({name:t[e].toName,value:r.concat(t[e].value)}),n&&a.push({name:t[e].fromName,value:n.concat(t[e].value)})}return a},initial:function(){var t=this;this.myChart3=i.a.init(document.getElementById("line-manage-chart"));var a=o["d"],e=[{name:a[0].fromName+" Top3",type:"lines",zlevel:1,effect:{show:!0,period:6,trailLength:0,color:"#E15536",symbolSize:10,symbol:"arrow"},lineStyle:{normal:{color:"#E15536",width:3,curveness:.2,type:"dashed"}},data:a.map((function(a){return{fromName:a.fromName,toName:a.toName,coords:[t.geoCoordMap[a.fromName],t.geoCoordMap[a.toName]],name:a.name}}))},{cursor:"auto",name:a[0].fromName+" Top3",type:"effectScatter",coordinateSystem:"geo",zlevel:2,rippleEffect:{brushType:"stroke"},label:{normal:{show:!0,position:"right",formatter:"{b}",color:"#333333"}},symbolSize:function(t){return t[2]/10},itemStyle:{normal:{color:{type:"radial",x:.5,y:.5,r:.5,colorStops:[{offset:0,color:"#E15536"},{offset:1,color:"#E15536"}],global:!1}},emphasis:{itemStyle:{color:"red"}}},data:this.convertData(a)}],r={tooltip:{position:function(t){return["0%","35%"]},borderColor:"#EBEEF5",borderWidth:1,padding:[15,18,15,18],trigger:"item",backgroundColor:"rgba(255,255,255,0.96)",formatter:function(t,a,e){var r=t.data.name,n=r.lineNumber,s=r.lineName,i=r.lineType,o=r.fromAgency,l=r.toAgency,c=r.distance,u=r.cost,m=r.time;return"effectScatter"===t.seriesType?void 0:"lines"===t.seriesType?'<span style="color:#20232A;font-weight:bold;margin-bottom:18px;display:inline-block">线路信息</span><br /><span style="color:#818693;font-size:12px;margin-right:100px;margin-bottom:4px;display:inline-block">线路编号：</span><span style="color:#20232A;font-size:12px;display:inline-block">'+n+'</span><br /><span style="color:#818693;font-size:12px;margin-right:100px;margin-bottom:4px;display:inline-block">线路名称：</span><span style="color:#20232A;font-size:12px">'+s+'</span><br /><span style="color:#818693;font-size:12px;margin-right:100px;margin-bottom:4px;display:inline-block">线路类型：</span><span style="color:#20232A;font-size:12px">'+i+'</span><br /><span style="color:#818693;font-size:12px;margin-right:88px;margin-bottom:4px;display:inline-block">起始地机构：</span><span style="color:#20232A;font-size:12px">'+o+'</span><br /><span style="color:#818693;font-size:12px;margin-right:88px;margin-bottom:4px;display:inline-block">目的地机构：</span><span style="color:#20232A;font-size:12px">'+l+'</span><br /><span style="color:#818693;font-size:12px;margin-right:123px;margin-bottom:4px;display:inline-block">距离：</span><span style="color:#20232A;font-size:12px">'+c+'</span><br /><span style="color:#818693;font-size:12px;margin-right:100px;margin-bottom:4px;display:inline-block">平均成本：</span><span style="color:#20232A;font-size:12px">'+u+'</span><br /><span style="color:#818693;font-size:12px;margin-right:100px;margin-bottom:4px;display:inline-block">预计时间：</span><span style="color:#20232A;font-size:12px">'+m+"</span>":t.name}},geo:{silent:!0,map:"china",label:{emphasis:{show:!0,color:"#fff"}},roam:!1,zoom:1.25,itemStyle:{normal:{borderColor:"#FDB892",borderWidth:0},emphasis:{areaColor:"#ba586f"}},regions:o["c"]},series:e};this.myChart3.clear(),this.myChart3.setOption(r),window.addEventListener("resize",(function(){t.myChart3.resize()}))}}},c=l,u=(e("1089"),e("2877")),m=Object(u["a"])(c,r,n,!1,null,"404afb9c",null);a["default"]=m.exports},d6ed:function(t,a){t.exports="data:image/png;base64,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"},dc02:function(t,a){t.exports="data:image/png;base64,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"},e555:function(t,a,e){},ea1b:function(t,a,e){}}]);