(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-e81afa88"],{"059d":function(t,e,a){},"05a0":function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACIAAAAiCAYAAAA6RwvCAAAEBElEQVRYR82Ya4iUZRTHf69ZrrYbeA3LUFPxgpet0C4YNeMlBVkV/aDijQiRKBARIwJF+5gfLCqRoCgh8oOoGwii7qgoeN9ZNTQv5Iql0A2czUuWb/yX8wzPPjsz+06zhQeWnXnfc87zf8/lf847EZXLAeAi8EYlrqJKjIGuwD3gB+DpSnxVCuQR4C5wBRj8fwJ5GJgGzAHGAsOBHgbgJnAOOAVsBQ4CcVJwSSOiJ18BrAZ6J3R+FVgLfJkEUBIgLwFfAUMCAKqNn+xPtfIE0B/oEuidABZYQRd9ho6ALAI+A7qZB4V6O/A1sBtoCTwrWjOAJUDKu/c7MBdoKIakFJCFwBbP8CjwFqAnTCKvAR8DQ035T2AScKiQcTEgLwIZLxJfAMsBOStHelrhTjGjX4AJ1u5t/BQCosI8CwwzTRXb0nJOD3TVaXuAV+y6Pk8N/RUCsgr4wBSPAy8bV+Rtz9fV1dzJ5aRX54X+ElBfVVOzYUR9fS44qI+19VN2faZ0fZ0QiKr/R6CfKb0AqDby0pROp+/D58TxwIJRiqLmLvD6uIaGsDD9mjsCKP15CYFMB3bZXSEW8jYg4jjeG0PJbosgjqJocgBGNkr5KHOoIr7snIcO1apueM0HvnGKSsftlpYzRSPRLulRc/fq6jFBmtYA60xV5OhKoN2TKWTPA/cBVbxou1WyqdS6GOQosUSwvjaTEbs6eRY4aV9EDYv9iIgJa+yCaPkx4IaxZN5DYzqdJY7HJUZBa/6ytZnMM56NznEPp5mUtns5peYYMD44oBEQ+rxkU6lcDNVlAmmpzWTcQzpTsfGjgZ/jArLRQzbGFL4DRv8HQHTeHUBcJXL83s5oCItVKXkc+C2cstlUqjGG2jIjEqZGfPKz+dCa4EiuXbGK1l81RZHPNXdwJxWramKf+dxsY6P1axgRtZMYU6IB94kD0knt+xHwtvlcZpO9IJDnvOmqNlMR57cssWoFhKYCFYEp9dpltLv86rdvmHYVqmO/eTY98zoVULxPZjuA2f7Bhah6li0/0tMGpqjof17c0IujaGYUx25zKzX0VOSHbb/926jhdEdAdH+vLTH6rAmsbeuPcjrG09UKKcZ2k3cT8Gboq9jwkrGI7kkzaLIB2FwmGJHiTmCA2YkoJwK3kgKRnpzs9+hf3PI+8GmCTU1j4h1gJVBlh4oKtFZozWgnHS3PYtdvgUGepRxts+sXgOvAQ7bFS1+rg+qsl2ejDtQS1abWktSIr9MX+BDQWlBI1N7FHugv46J3gdul0tpRRHxbbVTvAdrOtcmVEtWAWlSpPJ+krsoB4vxpXij8euUcCWhD1wDTUuxeOZXO8J2nJJ5/A8R3+MD8GvDAAFF0OuWHmn8AhAQVjmKpeZ0AAAAASUVORK5CYII="},"298c":function(t,e,a){"use strict";a.d(e,"e",(function(){return i})),a.d(e,"d",(function(){return l})),a.d(e,"c",(function(){return r})),a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return o}));var s=a("b775"),i=function(t){return Object(s["a"])("/transport-order-manager/count","get",t)},l=function(t){return Object(s["a"])("/transport-order-manager/page","post",t)},r=function(t){return Object(s["a"])("/transport-order-manager/".concat(t),"get",t)},n=function(t){return Object(s["a"])("/dispatch-configuration-manager","get",t)},o=function(t){return Object(s["a"])("/dispatch-configuration-manager","post",t)}},4517:function(t,e,a){"use strict";var s=a("059d"),i=a.n(s);i.a},"56dc":function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAkCAYAAACaJFpUAAAERUlEQVRYR62WW4xTVRSG/3/PTRiJ085wyTgwbWPCJYoYExPj5c1bfPICGtuSKJeIaMQHRRL1QUkQfRCjiAGCCe0xCl6eNBrevMTExIhouCSm7QzjhIGZtgQHZKbdv+kpMzIzPaft2PPQh57/X99e+6y19yKqPAvjmfC48JCg+wD0AuouWzgIoI/gty3El0OJULpaLNflJeqMZ5Zb2bclPFhTIOIrQ/PiSCJ0wk8/AyiJnbG+1y20DVBTLbD/NCwacMdIsvc1kqrknQLseeH0nL/PjR+E8Gh9oGlq4rNr57esHXhn8aXpcaYAA7HUIQmr/xfsipnE4VwyssYTGIimXxa0oxGwiRgEt+Wc8JtXx3QzLFXiZWtPAmhtJBDAWJsxy66uYBcYiGYOCjbeYFi5eWASOSe0djLrBeuHFo79c3EQkqkJSI64OqmzRr1tvWZu99n9C4fcBQTjmXXW2v3VzCS/aYK2DCcjp0rarlhqaRHcJen+al4Dsy7rhA64wI5o+nNAD/s3Kz/NOeHHK2kC0fQngh7zh/KLvBN+xAUGYqnfJKz0MpAcnTO3LTS4t3u4kqZ742DXpYuXM5LavWPgWC4ZubmcYSx1FsJ8H/H3uWTkbr8MArHUdxLu8tQQ5/LJyIIJ4BiEFk8g+HHOCUd9gdG0I+gJH+B4PhlxW44d0dSZUit6A/F7zol4bnm5rVLHBNzks6ihvBNZNFE0vwJa5b062ibTfPvIwcU/V9J0rj19W9EWfvJvKx7NO+FbykUTTe8R9LRvlZLHm1qb7hk+sKR0B04+XU/1dxfHikckrahS5R/mnPAmFxiMp1Zbi0PVeglglgY7YcyPrtbaO2SxFVCwmtcYrMkmIodd4A3PqW04mx4o9XI14yzfD3cFwz1/vsfLLrD0E4xm3rCwr8wyoK/NwGzPOqFXJ0RXbosz7WO6eFzCkkZCSfS3cu6KocSi0SnAcpapey34df1jhWdDFQ3NA9lk75GrFVNv/GjmGcHubkSWhNmcc0IfTI81Y4gKxjLrreweAM2zBBcMzaZsMlTxBqo4JgbifXdCdq+k5fVASZ4AzcZcovcHz432enHrRrWkRvs2gHaThBv9Dwb8AZk9kfbefb/s5bj/IVBDCoF4ZrOsfb+SlMY8m0uEav7unpP39OAdsfTzkHZN+Z/ckk+G361hzZOSmoElRyCefklWO90Tw3BrLhF+qx7Y5ElTj6kjnnJPpHwisr0e34zGn415Np66tnQ2AM/Gd6dvaiWFpbBaIrKbUBfATkjzBLYTmCOqGWL5UKAKFAsCLhEaBXkB0IjAYUqDMOwXcapNPDYxfbNUCJA2N/rgnpEZ0Q9ydwm4TcIGSOFGbJlnDDJNYt/kNww++ddiFQqroOIyij0W6CG1AMJ1IK6TOI9Eq6BmgFemPI0TLEgYI3UBwnkQ5yWeNcCAqAGw6SSbm49mP7r+dGkx/wJgKKe9yx0PswAAAABJRU5ErkJggg=="},7837:function(t,e,a){"use strict";var s=a("94e6"),i=a.n(s);i.a},"89cb":function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"add-form maps-details-box"},[a("el-dialog",{attrs:{title:t.titleInfo.text,visible:t.dialogFormVisible},on:{"update:visible":function(e){t.dialogFormVisible=e}}},[a("div",{staticClass:"content"},[a("baidu-map",{staticClass:"mapsClass",attrs:{center:t.center,zoom:t.zoom,"scroll-wheel-zoom":t.scrollWheelZoom},on:{ready:t.handler}},[a("bm-driving",{attrs:{panel:!1,start:t.startArea,end:t.endArea,"auto-viewport":!0,location:"中国"}})],1)],1)])],1)},i=[],l={props:{titleInfo:{type:Object,required:!0},formBase:{type:Object,required:!0}},data:function(){return{scrollWheelZoom:!0,zoom:6,center:{lng:116.404,lat:39.915},dialogFormVisible:!1,startArea:"",endArea:""}},computed:{},created:function(){},mounted:function(){},updated:function(){},methods:{handler:function(t){var e=t.BMap,a=t.map,s=this;s.startArea="",s.endArea="",s.BMap=e,s.map=a;var i=new e.Point(s.formBase.startLng,s.formBase.startLat),l=new e.Point(s.formBase.endLng,s.formBase.endLat),r=new e.Geocoder;r.getLocation(i,(function(t){s.startArea=t.address})),r.getLocation(l,(function(t){s.endArea=t.address}))},update:function(t){this.points=t.target.cornerPoints},dialogFormV:function(){this.dialogFormVisible=!0},dialogFormH:function(){this.dialogFormVisible=!1}}},r=l,n=(a("4517"),a("2877")),o=Object(n["a"])(r,s,i,!1,null,"e8b6ab24",null);e["default"]=o.exports},"94e6":function(t,e,a){},"9bd5":function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"dashboard-container waybill-detail"},[s("div",{staticClass:"app-container"},[s("el-card",{staticClass:"order-box",attrs:{shadow:"never"}},[s("el-row",{attrs:{gutter:20,justify:"center"}},[s("el-col",{attrs:{span:6}},[s("div",{staticClass:"ddbh"},[t._v("\n            订单编号：\n            "),s("label",[t._v(t._s(t.waybillDetailShow.orderId))])])]),t._v(" "),s("el-col",{attrs:{span:6}},[s("div",{staticClass:"ydbh"},[t._v("\n            运单编号：\n            "),s("label",[t._v(t._s(t.waybillDetailShow.ydId))])])]),t._v(" "),s("el-col",{attrs:{span:6}},[s("div",{staticClass:"xdsj"},[t._v("\n            下单时间：\n            "),s("label",[t._v(t._s(t.waybillDetailShow.createTime))])])]),t._v(" "),s("el-col",{attrs:{span:6}},[s("div",{staticClass:"ddzt"},[t._v("\n            订单状态：\n            ")," 23000"==t.waybillDetailShow.status?s("label",[t._v("待取件")]):"23001"==t.waybillDetailShow.status?s("label",[t._v("已取件")]):"23003"==t.waybillDetailShow.status?s("label",[t._v("网点入库")]):"23004"==t.waybillDetailShow.status?s("label",[t._v("待装车")]):"23005"==t.waybillDetailShow.status?s("label",[t._v("运输中")]):"23006"==t.waybillDetailShow.status?s("label",[t._v("网点出库")]):"23007"==t.waybillDetailShow.status?s("label",[t._v("待派送")]):"23008"==t.waybillDetailShow.status?s("label",[t._v("派送中")]):"23009"==t.waybillDetailShow.status?s("label",[t._v("已签收")]):"23010"==t.waybillDetailShow.status?s("label",[t._v("拒收")]):s("label",[t._v("已取消")])])]),t._v(" "),s("el-col",{attrs:{span:6}},[s("div",{staticClass:"yjddrq"},[t._v("\n            预计到达日期：\n            "),s("label",[t._v(t._s(t.waybillDetailShow.estimatedArrivalTime))])])])],1)],1),t._v(" "),s("el-collapse",{staticStyle:{"margin-top":"20px",border:"none"},model:{value:t.activeNames,callback:function(e){t.activeNames=e},expression:"activeNames"}},[s("el-collapse-item",{attrs:{name:"1"}},[s("template",{slot:"title"},[s("div",{staticClass:"collapse-detail"},[t._v("基本信息")])]),t._v(" "),s("div",{staticClass:"block"},[s("el-timeline",[s("div",{staticClass:"line"}),t._v(" "),s("div",{staticClass:"pake-info",staticStyle:{"margin-top":"10px"}},[s("img",{staticClass:"img-info1",attrs:{src:a("56dc"),alt:""}}),t._v(" "),s("span",[t._v("发货方")])]),t._v(" "),s("div",{staticStyle:{"margin-top":"0px","margin-left":"40px",display:"flex"}},[s("div",{},[s("div",{staticClass:"demo-input-suffix base-info"},[t._v("\n                  发货方姓名：\n                  "),s("span",[t._v(t._s(t.waybillDetailShow.senderName))])]),t._v(" "),s("div",{staticClass:"demo-input-suffix base-info"},[t._v("\n                  发货方地址：\n                  "),s("span",[t._v(t._s(t.waybillDetailShow.senderProvince+t.waybillDetailShow.senderCity+t.waybillDetailShow.senderCounty))])])]),t._v(" "),s("div",{staticStyle:{"margin-left":"174px"}},[s("div",{staticClass:"demo-input-suffix base-info"},[t._v("\n                  发货方电话：\n\n                  "),s("span",[t._v(t._s(t.waybillDetailShow.senderPhone))])]),t._v(" "),s("div",{staticClass:"demo-input-suffix base-info"},[t._v("\n                     详细地址：\n\n                  "),s("span",[t._v(t._s(t.waybillDetailShow.senderAddress))])])])]),t._v(" "),s("div",{staticClass:"pake-info",staticStyle:{"margin-top":"20px"}},[s("img",{staticClass:"img-info2",attrs:{src:a("05a0"),alt:""}}),t._v(" "),s("span",{staticStyle:{display:"inline-block","margin-top":"6px"}},[t._v("收货方")])]),t._v(" "),s("div",{staticStyle:{"margin-top":"0px","margin-left":"40px",display:"flex"}},[s("div",{},[s("div",{staticClass:"demo-input-suffix base-info"},[t._v("\n                  收货方姓名：\n                  "),s("span",[t._v(t._s(t.waybillDetailShow.receiverName))])]),t._v(" "),s("div",{staticClass:"demo-input-suffix base-info"},[t._v("\n                  收货方地址：\n                  "),s("span",[t._v(t._s(t.waybillDetailShow.receiverProvince+t.waybillDetailShow.receiverCity+t.waybillDetailShow.receiverCounty))])])]),t._v(" "),s("div",{staticStyle:{"margin-left":"174px"}},[s("div",{staticClass:"demo-input-suffix base-info"},[t._v("\n                  收货方电话：\n                  "),s("span",[t._v(t._s(t.waybillDetailShow.receiverPhone))])]),t._v(" "),s("div",{staticClass:"demo-input-suffix base-info"},[t._v("\n                     详细地址：\n                  "),s("span",[t._v(t._s(t.waybillDetailShow.receiverAddress))])])])])])],1)],2)],1),t._v(" "),s("el-collapse",{staticClass:"transport-box",staticStyle:{"margin-top":"20px"},model:{value:t.activeNames,callback:function(e){t.activeNames=e},expression:"activeNames"}},[s("el-collapse-item",{attrs:{name:"6"}},[s("template",{slot:"title"},[s("div",{staticClass:"collapse-detail"},[t._v("运输信息")])]),t._v(" "),s("el-scrollbar",{staticClass:"order-scroll",attrs:{"scroll-x":""}},[s("el-steps",{directives:[{name:"show",rawName:"v-show",value:t.waybillDetailShow.taskTransports&&t.waybillDetailShow.taskTransports.length>0,expression:"\n              waybillDetailShow.taskTransports &&\n                waybillDetailShow.taskTransports.length > 0\n            "}],attrs:{shadow:"never","align-center":"","finish-status":"success"}},t._l(t.waybillDetailShow.taskTransports,(function(e,a){return s("el-step",{key:e.id,class:{lastBefore:!(a!==t.waybillDetailShow.taskTransports.length-1-1),solid:4===e.status&&a===t.waybillDetailShow.taskTransports.length-1-1},attrs:{title:null===e.startAgency?"":e.startAgency.name,status:1===e.status?"wait":2===e.status?a===t.waybillDetailShow.taskTransports.length-1-1?"success":"process":"success"}},[a!==t.waybillDetailShow.taskTransports.length-1?s("template",{slot:"description"},[s("div",{staticClass:"step-row"},[s("table",{staticClass:"processing_content",attrs:{width:"100%",border:"0",cellspacing:"0",cellpadding:"0"}},[s("tr",[s("td",[t._v("运输任务：")]),t._v(" "),s("td",{staticClass:"num"},[t._v("\n                        "+t._s(null===e.id?"--":e.id)+"\n                      ")])]),t._v(" "),s("tr",[s("td",[t._v("车辆：")]),t._v(" "),s("td",[t._v("\n                        "+t._s(null===e.truck?"--":e.truck.licensePlate)+"\n                      ")])]),t._v(" "),s("tr",[s("td",[t._v("司机：")]),t._v(" "),s("td",[t._v("\n                        "+t._s(null===e.drivers?"--":e.drivers.map((function(t){return t.name})).join(","))+"\n                      ")])]),t._v(" "),s("tr",[s("td",[t._v("到达时间：")]),t._v(" "),s("td",[t._v("\n                        "+t._s(null===e.actualArrivalTime?"--":e.actualArrivalTime)+"\n                      ")])])])])]):t._e()],2)})),1)],1),t._v(" "),s("empty",{directives:[{name:"show",rawName:"v-show",value:!t.waybillDetailShow.taskTransports||t.waybillDetailShow.taskTransports.length<=0,expression:"\n            !waybillDetailShow.taskTransports ||\n              waybillDetailShow.taskTransports.length <= 0\n          "}],attrs:{src:"@/assets/empty.png",desc:"这里空空如也~"}})],2)],1),t._v(" "),s("el-collapse",{staticClass:"customer-table-box",staticStyle:{"margin-top":"20px","margin-bottom":"40px"},model:{value:t.activeNames,callback:function(e){t.activeNames=e},expression:"activeNames"}},[s("el-collapse-item",{attrs:{name:"3"}},[s("template",{slot:"title"},[s("div",{staticClass:"collapse-detail"},[t._v("货品信息")])]),t._v(" "),s("div",{staticStyle:{"box-sizing":"border-box"}},[s("el-table",{staticStyle:{width:"100%"},attrs:{data:t.propTableData.col,stripe:"","header-cell-style":{background:"rgba(250,252,255,1)"}}},[s("div",{directives:[{name:"show",rawName:"v-show",value:!t.propTableData.col||t.propTableData.col.length<=0,expression:"\n                (!propTableData.col || propTableData.col.length <= 0)\n              "}],attrs:{slot:"empty"},slot:"empty"},[s("img",{staticStyle:{"margin-top":"20px",width:"25%",height:"25%"},attrs:{src:a("aefe"),alt:"img"}}),t._v(" "),s("p",{staticStyle:{"margin-top":"-10px","padding-bottom":"10px"}},[t._v("\n                这里空空如也\n              ")])]),t._v(" "),s("el-table-column",{attrs:{label:"序号",type:"index",align:"left",width:"60"}}),t._v(" "),s("el-table-column",{attrs:{label:"货品名称"},scopedSlots:t._u([{key:"default",fn:function(e){return[s("span",[t._v(t._s(e.row.name||"--"))])]}}])}),t._v(" "),s("el-table-column",{attrs:{label:"货品类型"},scopedSlots:t._u([{key:"default",fn:function(e){return[s("span",[t._v(t._s(null===e.row.goodsType?"--":e.row.goodsType.name))])]}}])}),t._v(" "),s("el-table-column",{attrs:{label:"重量（千克）"},scopedSlots:t._u([{key:"default",fn:function(e){return[s("span",[t._v(t._s(e.row.totalWeight||"--"))])]}}])}),t._v(" "),s("el-table-column",{attrs:{label:"体积（立方）"},scopedSlots:t._u([{key:"default",fn:function(e){return[s("span",[t._v(t._s(e.row.totalVolume||"--"))])]}}])})],1)],1)],2)],1)],1),t._v(" "),s("waybillMapsDialog",{ref:"wayBillMaps",attrs:{"title-info":t.titleInfo,"form-base":t.formData}})],1)},i=[],l=(a("7f7f"),a("96cf"),a("3b8d")),r=a("89cb"),n=a("298c"),o={components:{waybillMapsDialog:r["default"]},data:function(){return{logisticsInfo:[],formData:{startLat:"",startLng:"",endLat:"",endLng:""},wayId:"",orderId:"",titleInfo:{pageTitle:"",text:""},activeNames:["1","2","3","4","5","6","7"],propTableData:{col:[]},waybillDetailShow:{orderId:"",ydId:"",createTime:"",status:"",estimatedArrivalTime:"",senderName:"",senderPhone:"",senderAddress:"",receiverName:"",receiverPhone:"",receiverAddress:"",senderProvince:"",senderCity:"",senderCounty:"",receiverProvince:"",receiverCity:"",receiverCounty:"",amount:"",paymentMethod:"",paymentStatus:"",agencyName:"",courierName:"",pickupType:"",zystatus:"",courierMobile:"",estimatedStartTime:"",actualEndTime:"",psagencyName:"",pscourierName:"",pszystatus:"",pscourierMobile:"",psestimatedStartTime:"",psactualEndTime:"",taskTransports:[],taskDispatch:{}}}},created:function(){this.wayId=this.$route.query.id,this.orderId=this.$route.query.orderId,this.getList(this.wayId),this.getOrderLocusParams(this.orderId)},mounted:function(){var t=this;this.$router.afterEach((function(e,a,s){t.$nextTick((function(){window.scrollTo(0,0)}))}))},methods:{hadlleCancel:function(){history.go(-1)},getList:function(){var t=Object(l["a"])(regeneratorRuntime.mark((function t(e){var a,s;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(n["c"])(e);case 2:a=t.sent,s=a.data,this.waybillDetailShow.ydId=s.id,this.waybillDetailShow.taskDispatch=s.taskDispatch,this.waybillDetailShow.taskRejectionDispatch=s.taskRejectionDispatch,this.waybillDetailShow.taskPickup=s.taskPickup,null!=s.order&&(this.waybillDetailShow.orderId=s.order.id,this.waybillDetailShow.createTime=s.order.createTime,this.waybillDetailShow.status=s.order.status,this.waybillDetailShow.estimatedArrivalTime=s.order.estimatedArrivalTime,this.waybillDetailShow.senderName=s.order.senderName,this.waybillDetailShow.senderPhone=s.order.senderPhone,this.waybillDetailShow.senderAddress=s.order.senderAddress,this.waybillDetailShow.receiverName=s.order.receiverName,this.waybillDetailShow.receiverPhone=s.order.receiverPhone,this.waybillDetailShow.receiverAddress=s.order.receiverAddress,this.waybillDetailShow.amount=s.order.amount,this.propTableData.col=s.order.orderCargoDTOS,1===s.paymentMethod?this.waybillDetailShow.paymentMethod="预结":this.waybillDetailShow.paymentMethod="到付",1===s.order.paymentStatus?this.waybillDetailShow.paymentStatus="未付":this.waybillDetailShow.paymentStatus="已付",null!==s.senderProvince&&(this.waybillDetailShow.senderProvince=s.order.senderProvince.name),null!==s.senderCity&&(this.waybillDetailShow.senderCity=s.order.senderCity.name),null!==s.senderCounty&&(this.waybillDetailShow.senderCounty=s.order.senderCounty.name),null!==s.receiverProvince&&(this.waybillDetailShow.receiverProvince=s.order.receiverProvince.name),null!==s.receiverCity&&(this.waybillDetailShow.receiverCity=s.order.receiverCity.name),null!==s.receiverCounty&&(this.waybillDetailShow.receiverCounty=s.order.receiverCounty.name),1===s.order.pickupType?this.waybillDetailShow.pickupType="网点自寄":this.waybillDetailShow.pickupType="上门取件",this.formData.startLat=s.order.senderCity.lat,this.formData.startLng=s.order.senderCity.lng,this.formData.endLat=s.order.receiverCity.lat,this.formData.endLng=s.order.receiverCity.lng),s.taskTransports.length&&(this.waybillDetailShow.taskTransports=s.taskTransports.reverse(),this.waybillDetailShow.taskTransports.push(Object.assign({},{startAgency:{name:this.waybillDetailShow.taskTransports[this.waybillDetailShow.taskTransports.length-1].endAgency.name},status:this.waybillDetailShow.taskTransports[this.waybillDetailShow.taskTransports.length-1].status})));case 10:case"end":return t.stop()}}),t,this)})));function e(e){return t.apply(this,arguments)}return e}()}},c=o,d=(a("7837"),a("2877")),p=Object(d["a"])(c,s,i,!1,null,"39f39986",null);e["default"]=p.exports},aefe:function(t,e,a){t.exports=a.p+"static/img/pic-kong.742d3899.png"}}]);