(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-77304f75"],{2934:function(t,e,a){"use strict";a.d(e,"e",(function(){return i})),a.d(e,"f",(function(){return r})),a.d(e,"d",(function(){return s})),a.d(e,"a",(function(){return l})),a.d(e,"b",(function(){return o}));var n=a("b775"),i=function(t){return Object(n["a"])("/web-manager/common/transportLineType/simple","get",t)},r=function(t){return Object(n["a"])("/truckType/simple","get",t)},s=function(t){return Object(n["a"])("/web-manager/common/fleet/simple","get",t)},l=function(t){return Object(n["a"])("/areas/children","get",t)},o=function(t){return Object(n["a"])("/workspace","get",t)}},"298c":function(t,e,a){"use strict";a.d(e,"e",(function(){return i})),a.d(e,"d",(function(){return r})),a.d(e,"c",(function(){return s})),a.d(e,"a",(function(){return l})),a.d(e,"b",(function(){return o}));var n=a("b775"),i=function(t){return Object(n["a"])("/transport-order-manager/count","get",t)},r=function(t){return Object(n["a"])("/transport-order-manager/page","post",t)},s=function(t){return Object(n["a"])("/transport-order-manager/".concat(t),"get",t)},l=function(t){return Object(n["a"])("/dispatch-configuration-manager","get",t)},o=function(t){return Object(n["a"])("/dispatch-configuration-manager","post",t)}},"35d2":function(t,e,a){},"52c0":function(t,e,a){},"53ea":function(t,e,a){"use strict";var n=a("35d2"),i=a.n(n);i.a},5850:function(t,e,a){"use strict";a.d(e,"a",(function(){return i})),a.d(e,"b",(function(){return r})),a.d(e,"c",(function(){return s})),a.d(e,"d",(function(){return l}));var n=a("b775"),i=function(t){return Object(n["a"])("/business-hall/tree","get",t)},r=function(t){return Object(n["a"])("/business-hall/".concat(t),"get",t)},s=function(t){return Object(n["a"])("/business-hall/user/page","get",t)},l=function(t){return Object(n["a"])("/business-hall","post",t)}},"5ae1":function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAcCAYAAAB2+A+pAAAAAXNSR0IArs4c6QAAAP1JREFUSA1jYBgFoyEwGgLDNgT+///PQonnyNJ///4riRt3H1++ce9JKDmW37j3NBmo//TDhx8Ecelnwibxm+FnGFBch+E/wzJSLQdZysjwfzZQv8mPv198sJkPEsNqsZqi7CQGRsYWBgZgcJNgOcxSYDAzMjIwFqsrySzGZTFecaBvm4FB9v/G3Se/CfkcZOnNe0/+gdTfvPukCK/BxEgSYznVLYU5DJ/lNLMUn+WUWMoIM5gYGuRzhv//axgYGP8AU888oJ5UeEJSlukjxgyYGpIsBmlCWA4xApx6SbQUZjnJNCzOqZJ6SbX9zoOnVqTqGVU/GgKjITC8QgAAQhHA2apbODgAAAAASUVORK5CYII="},"5f87":function(t,e,a){"use strict";a.d(e,"a",(function(){return s}));var n=a("a78e"),i=a.n(n),r="vue_admin_template_token";function s(t){return i.a.set(r,t)}},"8fc7":function(t,e,a){"use strict";var n=a("a768"),i=a.n(n);i.a},a586:function(t,e,a){t.exports=a.p+"static/img/<EMAIL>"},a768:function(t,e,a){},b57e:function(t,e,a){"use strict";var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-card",{staticClass:"tabchange-card",attrs:{shadow:"never"}},[a("div",{staticClass:"tab-change"},t._l(t.settingList,(function(e){return a("div",{key:e.value,staticClass:"tab-item",class:{active:e.value==t.activeIndex},on:{click:function(a){return t.tabChange(e.value)}}},[a("span",{staticClass:"status"},[t._v(t._s(e.label))]),t._v(" "),t.isShowNum?a("span",{staticClass:"status-num",staticStyle:{"font-weight":"bold"}},[t._v(t._s(e.num))]):t._e()])})),0)])},i=[],r=(a("c5f6"),{name:"TabChange",props:{defaultActiveIndex:{type:Number,required:!0},settingList:{type:Array,required:!0},isShowNum:{type:Boolean,default:!0}},data:function(){return{activeIndex:this._props.defaultActiveIndex||0}},methods:{tabChange:function(t){this.activeIndex=t,this.$emit("tabChange",t)}}}),s=r,l=(a("53ea"),a("2877")),o=Object(l["a"])(s,n,i,!1,null,null,null);e["a"]=o.exports},b918:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{ref:"contentData",staticClass:"dashboard-container waybill-list customer-list-box"},[n("div",{staticClass:"app-container"},[n("el-card",{staticClass:"search-card-box",attrs:{shadow:"never"}},[n("el-form",{ref:"waybillSearchFormData",attrs:{model:t.waybillSearchFormData,"label-width":"80px"}},[n("el-row",{attrs:{gutter:60}},[n("el-col",{attrs:{span:8}},[n("el-form-item",{staticStyle:{"margin-bottom":"0px"},attrs:{label:"运单编号:","label-width":"110px"}},[n("el-input",{attrs:{placeholder:"请输入运单编号"},model:{value:t.waybillSearchFormData.id,callback:function(e){t.$set(t.waybillSearchFormData,"id",e)},expression:"waybillSearchFormData.id"}})],1)],1),t._v(" "),n("el-col",{attrs:{span:8}},[n("el-form-item",{staticStyle:{"margin-bottom":"0px"},attrs:{label:"订单编号:","label-width":"110px"}},[n("el-input",{attrs:{placeholder:"请输入订单编号"},model:{value:t.waybillSearchFormData.orderId,callback:function(e){t.$set(t.waybillSearchFormData,"orderId",e)},expression:"waybillSearchFormData.orderId"}})],1)],1),t._v(" "),n("el-col",{attrs:{span:8}},[n("el-form-item",{staticStyle:{"margin-bottom":"20px"},attrs:{label:"调度状态:","label-width":"110px"}},[n("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:t.waybillSearchFormData.schedulingStatus,callback:function(e){t.$set(t.waybillSearchFormData,"schedulingStatus",e)},expression:"waybillSearchFormData.schedulingStatus"}},t._l(t.schedulingStatus,(function(t){return n("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1)],1),t._v(" "),n("el-col",{attrs:{span:8}},[n("el-form-item",{staticStyle:{"margin-bottom":"20px"},attrs:{label:"起始地机构:","label-width":"110px"}},[n("treeselect",{staticStyle:{width:"100%"},attrs:{options:t.agencyOptions,normalizer:t.normalizer,searchable:!0,placeholder:"请选择起始地机构","no-options-text":"暂无数据","no-results-text":"暂无数据"},on:{select:t.selectStartAgency,open:t.open,close:t.close},model:{value:t.waybillSearchFormData.startAgencyId,callback:function(e){t.$set(t.waybillSearchFormData,"startAgencyId",e)},expression:"waybillSearchFormData.startAgencyId"}}),t._v(" "),n("img",{ref:"arrow",staticStyle:{position:"absolute",width:"18px",height:"18px",right:"9px",top:"12px",cursor:"pointer","pointer-events":"none","transition-duration":"0.3s"},attrs:{src:a("5ae1")}})],1)],1),t._v(" "),n("el-col",{attrs:{span:8}},[n("el-form-item",{staticStyle:{"margin-bottom":"20px"},attrs:{label:"目的地机构:","label-width":"110px"}},[n("treeselect",{staticStyle:{width:"100%"},attrs:{options:t.agencyOptions,normalizer:t.normalizer,searchable:!0,placeholder:"请选择目的地机构","no-options-text":"暂无数据","no-results-text":"暂无数据"},on:{select:t.selectEndAgency,open:t.opens,close:t.closes},model:{value:t.waybillSearchFormData.endAgencyId,callback:function(e){t.$set(t.waybillSearchFormData,"endAgencyId",e)},expression:"waybillSearchFormData.endAgencyId"}}),t._v(" "),n("img",{ref:"arrows",staticStyle:{position:"absolute",width:"18px",height:"18px",right:"9px",top:"12px",cursor:"pointer","pointer-events":"none","transition-duration":"0.3s"},attrs:{src:a("5ae1")}})],1)],1),t._v(" "),n("el-col",{attrs:{span:8}},[n("el-form-item",{staticStyle:{"margin-bottom":"20px"},attrs:{label:"当前所在机构:","label-width":"110px"}},[n("treeselect",{staticStyle:{width:"100%"},attrs:{options:t.agencyOptions,normalizer:t.normalizer,searchable:!0,placeholder:"请选择当前所在机构","no-options-text":"暂无数据","no-results-text":"暂无数据"},on:{select:t.selectCurrentAgency,open:t.opensCurrentAgency,close:t.closesCurrentAgency},model:{value:t.waybillSearchFormData.currentAgencyId,callback:function(e){t.$set(t.waybillSearchFormData,"currentAgencyId",e)},expression:"waybillSearchFormData.currentAgencyId"}}),t._v(" "),n("img",{ref:"arrowsCurrentAgency",staticStyle:{position:"absolute",width:"18px",height:"18px",right:"9px",top:"12px",cursor:"pointer","pointer-events":"none","transition-duration":"0.3s"},attrs:{src:a("5ae1")}})],1)],1),t._v(" "),n("el-col",{staticStyle:{"padding-left":"50px"},attrs:{span:8}},[n("el-button",{attrs:{type:"warning"},on:{click:function(e){return t.handleFilter("查询")}}},[t._v("搜索")]),t._v(" "),n("el-button",{staticClass:"reset-btn",attrs:{plain:""},on:{click:function(e){return t.resetForm("waybillSearchFormData")}}},[t._v("重置")])],1)],1)],1)],1),t._v(" "),n("TabChange",{attrs:{"default-active-index":0,"setting-list":t.settingList},on:{tabChange:t.tabChange}}),t._v(" "),n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],class:{"loading-box":t.listLoading},staticStyle:{"margin-top":"20px"},attrs:{"element-loading-text":"加载中"}},[n("el-card",{staticClass:"table-card-box",attrs:{shadow:"never"}},[n("el-button",{staticStyle:{"margin-bottom":"20px"},attrs:{type:"warning"},on:{click:function(e){return t.handleDispatch()}}},[t._v("调度配置")]),t._v(" "),n("el-table",{key:t.tableKey,ref:"multipleTable",staticStyle:{width:"100%"},attrs:{data:t.dataList,fit:"",stripe:"","header-cell-style":{background:"rgba(250,252,255,1)"}},on:{"selection-change":t.handleSelectionChange}},[n("div",{directives:[{name:"show",rawName:"v-show",value:(!t.dataList||t.dataList.length<=0)&&!t.listLoading&&!t.searchkey,expression:"(!dataList || dataList.length <= 0) && !listLoading && !searchkey"}],attrs:{slot:"empty"},slot:"empty"},[n("img",{staticStyle:{"margin-top":"20px",width:"14%",height:"14%"},attrs:{src:a("a586"),alt:"img"}}),t._v(" "),n("p",{staticStyle:{"margin-top":"-20px","padding-bottom":"0px"}},[t._v("这里空空如也")])]),t._v(" "),n("el-card",{directives:[{name:"show",rawName:"v-show",value:(!t.dataList||t.dataList.length<=0)&&!t.listLoading&&t.searchkey,expression:"(!dataList || dataList.length <= 0) && !listLoading && searchkey"}],staticClass:"table-empty-box",attrs:{slot:"empty",shadow:"never"},slot:"empty"},[n("empty")],1),t._v(" "),n("el-table-column",{attrs:{align:"left",type:"index",label:"序号",width:"60"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(e.$index+(t.waybillSearchFormData.page-1)*t.waybillSearchFormData.pageSize+1))])]}}])}),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"运单编号","min-width":"160"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(e.row.id))])]}}])}),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"订单编号","min-width":"160"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(e.row.orderId))])]}}])}),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"运单状态","min-width":"130"},scopedSlots:t._u([{key:"default",fn:function(e){return[1==e.row.status?n("span",[t._v("新建")]):2==e.row.status?n("span",[t._v("已装车")]):3==e.row.status?n("span",[t._v("到达")]):4==e.row.status?n("span",[t._v("到达终端网点")]):5==e.row.status?n("span",[t._v("已签收")]):n("span",[t._v("拒收")])]}}])}),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"调度状态","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[1==e.row.schedulingStatus?n("span",[t._v("待调度")]):2==e.row.schedulingStatus?n("span",[t._v("未匹配线路")]):n("span",[t._v("已调度")])]}}])}),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"起始营业部","min-width":"170",prop:"startAgencyName"}}),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"终点营业部","min-width":"170",prop:"endAgencyName"}}),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"当前所在机构","min-width":"170",prop:"currentAgencyName"}}),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"下一个机构","min-width":"170",prop:"nextAgencyName"}}),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"货品总体积(立方米)","min-width":"170",prop:"totalVolume"}}),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"货品总重量(千克)","min-width":"170",prop:"totalWeight"}}),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"创建时间","min-width":"170",prop:"created"}}),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"更新时间","min-width":"170",prop:"updated"}}),t._v(" "),n("el-table-column",{attrs:{align:"center",fixed:"right",label:"操作",width:"85","class-name":"small-padding fixed-width"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(a){return t.handleWayBillDetail(e.row.id,e.row.order)}}},[t._v("查看详情")])]}}])})],1),t._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:t.dataList&&t.dataList.length>0,expression:"dataList && dataList.length > 0"}],staticClass:"pagination"},[n("div",{staticClass:"pages"},[n("el-pagination",{attrs:{"current-page":Number(t.waybillSearchFormData.page),total:Number(t.total),"page-size":Number(t.waybillSearchFormData.pageSize),"page-sizes":[10,20,30,50],layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)])],1)],1)],1),t._v(" "),n("el-dialog",{staticClass:"add-form-dialog",attrs:{title:"运输配置",visible:t.dialogVisible,width:"600px","before-close":t.handleClose},on:{"update:visible":function(e){t.dialogVisible=e}}},[n("el-form",{ref:"dataForm",staticStyle:{width:"100%"},attrs:{"label-position":"right","label-width":"140px",rules:t.ruleInline,model:t.formBase}},[n("el-form-item",{attrs:{label:"最晚任务下发时间",prop:"dispatchTime"}},[n("el-input",{attrs:{placeholder:"请输入最晚任务下发时间"},model:{value:t.formBase.dispatchTime,callback:function(e){t.$set(t.formBase,"dispatchTime",e)},expression:"formBase.dispatchTime"}},[n("span",{staticStyle:{color:"#20232a","font-weight":"400","font-size":"14px","font-family":"PingFangSC, PingFangSC-Regular","margin-right":"14px"},attrs:{slot:"suffix"},slot:"suffix"},[t._v("小时前")])])],1),t._v(" "),n("el-form-item",{attrs:{label:"优先匹配方式",required:""}},[n("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择优先匹配方式",clearable:""},model:{value:t.formBase.dispatchMethod,callback:function(e){t.$set(t.formBase,"dispatchMethod",e)},expression:"formBase.dispatchMethod"}},t._l(t.dispatchWaybillList,(function(t){return n("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1)],1),t._v(" "),n("div",{staticClass:"dialog-footer",staticStyle:{"text-align":"center"},attrs:{slot:"footer"},slot:"footer"},[n("el-button",{staticClass:"save-btn",on:{click:t.confirmDispatch}},[t._v("确认")]),t._v(" "),n("el-button",{staticClass:"cancel-btn",on:{click:t.handleClose}},[t._v("取消")])],1)],1)],1)},i=[],r=a("db72"),s=(a("96cf"),a("3b8d")),l=(a("c5f6"),a("7f7f"),a("5f87")),o=a("298c"),c=a("cf92"),u=a("2934"),d=a("5850"),h=a("b57e"),m=(a("542c"),a("ca17")),p=a.n(m),g={components:{TabChange:h["a"],Treeselect:p.a},data:function(){return{agencyOptions:[],normalizer:function(t){return{id:t.id,label:t.name,children:t.children}},schedulingStatus:[{id:1,name:"待调度"},{id:2,name:"未匹配线路"},{id:3,name:"已调度"}],dispatchWaybillList:[{value:1,label:"转运次数最少"},{value:2,label:"成本最低"}],formBase:{dispatchMethod:2,dispatchTime:""},ruleInline:{dispatchTime:[{required:!0,validator:function(t,e,a){if(e){var n=/^(?:[1-9]|([1-3][0-9])?|4[0-8])$/;n.test(Number(e))?a():a(new Error("请输入1-48正整数"))}else a(new Error("最晚任务下发时间不能为空"))},trigger:"blur"}]},dialogVisible:!1,settingList:[{value:0,label:"全部",num:0},{value:1,label:"新建",num:0},{value:2,label:"已装车",num:0},{value:3,label:"运输中",num:0},{value:4,label:"到达终端网点",num:0},{value:6,label:"拒收",num:0}],provinceList:[],statusOptions:"",searchkey:!1,multipleSelection:[],tableKey:0,dataList:[],cityList:[],countryList:[],receiverCityList:[],countryReceiverList:[],total:null,listLoading:!0,alertText:"",waybillSearchFormData:{page:1,pageSize:10,id:"",orderId:""},requestParamsProvince:{}}},mounted:function(){window.addEventListener("message",(function(t){Object(l["a"])(t.data)}),!1)},created:function(){this.statusOptions=c;var t=this;this.companyId=this.$route.query.companyId,this.initialDate(),this.getAgencyList(),this.getConfig(),document.onkeydown=function(e){var a=window.event.keyCode;13===a&&t.handleFilter(this.waybillSearchFormData)}},methods:{getAgencyList:function(){var t=Object(s["a"])(regeneratorRuntime.mark((function t(){var e,a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(d["a"])();case 2:e=t.sent,a=e.data,this.agencyOptions=JSON.parse(a);case 5:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),getConfig:function(){var t=this;Object(o["a"])().then((function(e){200===e.code&&(t.formBase=e.data)})).catch((function(e){t.$message.error(e.msg||"获取调度配置失败")}))},confirmDispatch:function(){var t=this;this.$refs["dataForm"].validate(function(){var e=Object(s["a"])(regeneratorRuntime.mark((function e(a){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!a){e.next=5;break}return e.next=3,Object(o["b"])(t.formBase).then((function(e){"200"===String(e.code)?(t.$message({message:"操作成功！",type:"success"}),t.handleClose(),t.getConfig()):t.$message({message:e.msg,type:"error"})}));case 3:e.next=6;break;case 5:t.$message.error("*号为必填项!");case 6:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())},handleDispatch:function(){this.dialogVisible=!0},handleClose:function(){this.dialogVisible=!1},initialDate:function(){this.getList(),this.getWaybillStatic()},getCity:function(){var t=Object(s["a"])(regeneratorRuntime.mark((function t(){var e,a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(0===this.waybillSearchFormData.senderPro){t.next=8;break}return this.requestParamsProvince.parentId=this.waybillSearchFormData.senderCi,t.next=4,Object(u["a"])(this.requestParamsProvince);case 4:e=t.sent,a=e.data,this.countryList=a,this.waybillSearchFormData.sendercountyStr="";case 8:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),opens:function(){this.$refs.arrows.style.transform="rotate(-180deg)"},closes:function(){this.$refs.arrows.style.transform="rotate(0deg)"},open:function(){this.$refs.arrow.style.transform="rotate(-180deg)"},close:function(){this.$refs.arrow.style.transform="rotate(0deg)"},opensCurrentAgency:function(){this.$refs.arrowsCurrentAgency.style.transform="rotate(-180deg)"},closesCurrentAgency:function(){this.$refs.arrowsCurrentAgency.style.transform="rotate(0deg)"},selectStartAgency:function(t){this.waybillSearchFormData.startAgencyId=t.id},selectEndAgency:function(t){this.waybillSearchFormData.endAgencyId=t.id},selectCurrentAgency:function(t){this.waybillSearchFormData.currentAgencyId=t.id},handleSelectionChange:function(t){this.multipleSelection=t},tabChange:function(t){this.waybillSearchFormData.status=t,this.waybillSearchFormData.page=1,this.getList()},getWaybillStatic:function(){var t=Object(s["a"])(regeneratorRuntime.mark((function t(){var e,a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(o["e"])();case 2:e=t.sent,a=e.data,this.settingList[1].num=a[1]||0,this.settingList[2].num=a[0]||0,this.settingList[3].num=a[3]||0,this.settingList[4].num=a[4]||0,this.countFive=a[5]||0,this.settingList[5].num=a[6]||0,this.settingList[0].num=Number(this.settingList[1].num)+Number(this.settingList[2].num)+Number(this.settingList[3].num)+Number(this.settingList[4].num)+Number(this.countFive)+Number(this.settingList[5].num);case 11:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),getList:function(){var t=Object(s["a"])(regeneratorRuntime.mark((function t(){var e,a,n;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return this.listLoading=!0,e=Object(r["a"])({},this.waybillSearchFormData),0===e.status&&delete e.status,t.next=5,Object(o["d"])(e);case 5:a=t.sent,n=a.data,this.listLoading=!1,this.dataList=n.items,this.total=n.counts;case 10:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),resetForm:function(t){this.waybillSearchFormData={page:1,pageSize:10,status:this.waybillSearchFormData.status},this.searchkey=!1,this.getList()},handleFilter:function(){this.waybillSearchFormData.page=1,this.getList(this.waybillSearchFormData),this.searchkey=!0},handleSizeChange:function(t){this.waybillSearchFormData.pageSize=t,1===this.waybillSearchFormData.page&&this.getList(this.waybillSearchFormData)},handleCurrentChange:function(t){this.waybillSearchFormData.page=t,this.getList()},handleWayBillDetail:function(t,e){this.$router.push({path:"/business/waybill-detail",query:{id:t,orderId:e}})}}},b=g,f=(a("dab2"),a("8fc7"),a("2877")),y=Object(f["a"])(b,n,i,!1,null,"04484a66",null);e["default"]=y.exports},cf92:function(t){t.exports=[{id:1,name:"新建"},{id:2,name:"已装车"},{id:3,name:"到达"},{id:4,name:"到达终端网点"}]},dab2:function(t,e,a){"use strict";var n=a("52c0"),i=a.n(n);i.a}}]);