(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4736f188"],{"0a86":function(e,t,r){"use strict";r.d(t,"e",(function(){return n})),r.d(t,"f",(function(){return i})),r.d(t,"d",(function(){return s})),r.d(t,"a",(function(){return o})),r.d(t,"b",(function(){return l})),r.d(t,"c",(function(){return c}));var a=r("b775"),n=function(e){return Object(a["a"])("/transport-task-manager/page","post",e)},i=function(e){return Object(a["a"])("/transport-task-manager/count","get",e)},s=function(e){return Object(a["a"])("/transport-task-manager/".concat(e),"get",e)},o=function(e){return Object(a["a"])("/transport-task-manager/cancel/".concat(e),"put")},l=function(e){return Object(a["a"])("/transport-task-manager/adjust/".concat(e.id),"put",e)},c=function(e){return Object(a["a"])("/workingTrucks","get",e)}},"13b5":function(e,t,r){},2765:function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-dialog",{attrs:{title:e.titleInfo.text,visible:e.dialogFormVisible,width:"600px"},on:{"update:visible":function(t){e.dialogFormVisible=t}}},[a("el-form",{ref:"dataForm",staticStyle:{width:"100%"},attrs:{rules:e.ruleInline,model:e.formBase,"label-position":"right","label-width":"110px"}},[a("el-form-item",{attrs:{label:"线路编号：",prop:"number"}},[a("el-input",{attrs:{placeholder:"请输入线路名称",maxlength:"8"},model:{value:e.formBase.number,callback:function(t){e.$set(e.formBase,"number",t)},expression:"formBase.number"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"线路名称：",prop:"name"}},[a("el-input",{attrs:{placeholder:"请输入线路名称",maxlength:"15"},model:{value:e.formBase.name,callback:function(t){e.$set(e.formBase,"name",t)},expression:"formBase.name"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"线路类型：",prop:"type"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择线路类型",clearable:"","value-key":"id",disabled:"编辑线路"===e.titleInfo.text},on:{change:e.selectEndGet},model:{value:e.formBase.type,callback:function(t){e.$set(e.formBase,"type",t)},expression:"formBase.type"}},e._l(e.options,(function(e){return a("el-option",{key:e.id,attrs:{value:e.id,label:e.name}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"起始地机构：",prop:"startAgency"}},[a("treeselect",{staticStyle:{width:"100%"},attrs:{options:e.agencyOptions,normalizer:e.normalizer,searchable:!0,placeholder:"请选择起始地机构",disabled:"编辑线路"===e.titleInfo.text,"no-options-text":"暂无数据","no-results-text":"暂无数据"},on:{select:e.selectDepart,open:e.open,close:e.close},model:{value:e.formBase.startAgency,callback:function(t){e.$set(e.formBase,"startAgency",t)},expression:"formBase.startAgency"}}),e._v(" "),a("img",{ref:"arrow",staticStyle:{position:"absolute",width:"18px",height:"18px",right:"9px",top:"12px",cursor:"pointer","pointer-events":"none","transition-duration":"0.3s"},attrs:{src:r("5ae1")}})],1),e._v(" "),a("el-form-item",{attrs:{label:"目的地机构：",prop:"endAgency"}},[a("treeselect",{staticStyle:{width:"100%"},attrs:{options:e.agencyOptions,normalizer:e.normalizer,searchable:!0,placeholder:"请选择目的地机构",disabled:"编辑线路"===e.titleInfo.text,"no-options-text":"暂无数据","no-results-text":"暂无数据"},on:{select:e.selectDepartEnd,open:e.opens,close:e.closes},model:{value:e.formBase.endAgency,callback:function(t){e.$set(e.formBase,"endAgency",t)},expression:"formBase.endAgency"}}),e._v(" "),a("img",{ref:"arrows",staticStyle:{position:"absolute",width:"18px",height:"18px",right:"9px",top:"12px",cursor:"pointer","pointer-events":"none","transition-duration":"0.3s"},attrs:{src:r("5ae1")}})],1),e._v(" "),"编辑线路"===e.titleInfo.text?a("el-form-item",{attrs:{label:"距离：",prop:"distance"}},[a("el-input",{attrs:{placeholder:"请输入距离",maxlength:"10"},model:{value:e.formBase.distance,callback:function(t){e.$set(e.formBase,"distance",t)},expression:"formBase.distance"}},[a("span",{staticStyle:{color:"#20232a","font-weight":"400","font-size":"14px","font-family":"PingFangSC, PingFangSC-Regular","margin-right":"14px"},attrs:{slot:"suffix"},slot:"suffix"},[e._v("千米")])])],1):e._e(),e._v(" "),"编辑线路"===e.titleInfo.text?a("el-form-item",{attrs:{label:"成本：",prop:"cost"}},[a("el-input",{attrs:{placeholder:"请输入成本",maxlength:"10"},model:{value:e.formBase.cost,callback:function(t){e.$set(e.formBase,"cost",t)},expression:"formBase.cost"}},[a("span",{staticStyle:{color:"#20232a","font-weight":"400","font-size":"14px","font-family":"PingFangSC, PingFangSC-Regular","margin-right":"14px"},attrs:{slot:"suffix"},slot:"suffix"},[e._v("元")])])],1):e._e()],1),e._v(" "),a("div",{staticClass:"dialog-footer",staticStyle:{"text-align":"center"},attrs:{slot:"footer"},slot:"footer"},[a("el-button",{staticClass:"save-btn",on:{click:e.createData}},[e._v("确认")]),e._v(" "),a("el-button",{staticClass:"cancel-btn",on:{click:e.dialogFormH}},[e._v("取消")])],1)],1)],1)},n=[],i=(r("96cf"),r("3b8d")),s=(r("7f7f"),r("7b11")),o=r("ca17"),l=r.n(o),c=(r("542c"),{components:{Treeselect:l.a},props:{titleInfo:{type:Object,required:!0},formBase:{type:Object,required:!0},agencyOptions:{type:Array,required:!0}},data:function(){return{normalizer:function(e){return{id:e.id,label:e.name,children:e.children}},options:[{id:1,name:"干线"},{id:2,name:"支线"},{id:3,name:"接驳路线"}],startAgency:[],endAgency:[],transportLineType:[],dialogFormVisible:!1,ruleInline:{number:[{required:!0,validator:function(e,t,r){if(t){var a=/[A-Z]{2}\d{6}/;a.test(t)?r():r(new Error("线路编号前两位为XL，后6位位数字，请重新输入"))}else r(new Error("线路编号不能为空"))},trigger:"blur"}],type:[{required:!1,validator:function(e,t,r){t?r():r(new Error("线路类型不能为空"))},trigger:"blur"}],distance:[{required:!0,validator:function(e,t,r){if(t){var a=/^[0-9]+(.[0-9]{1,2})?$/;a.test(t)?r():r(new Error("只能输入数字类型，最多保留两位小数，请重新输入"))}else r(new Error("距离不能为空"))},trigger:"blur"}],cost:[{required:!0,validator:function(e,t,r){if(t){var a=/^[0-9]+(.[0-9]{1,2})?$/;a.test(t)?r():r(new Error("只能输入数字类型，最多保留两位小数，请重新输入"))}else r(new Error("成本不能为空"))},trigger:"blur"}],estimatedTime:[{required:!0,validator:function(e,t,r){if(t){var a=/^[0-9]+(.[0-9]{1,2})?$/;a.test(t)?r():r(new Error("只能输入数字类型，最多保留两位小数，请重新输入"))}else r(new Error("预计时间不能为空"))},trigger:"blur"}],name:[{required:!0,message:"线路名称不能为空",trigger:"blur"}],startAgency:[{required:!0,message:"起始地机构不能为空",trigger:"blur"}],endAgency:[{required:!0,message:"目的地机构不能为空",trigger:"blur"}]},flag:!0}},methods:{opens:function(){this.$refs.arrows.style.transform="rotate(-180deg)"},closes:function(){this.$refs.arrows.style.transform="rotate(0deg)"},open:function(){this.$refs.arrow.style.transform="rotate(-180deg)"},close:function(){this.$refs.arrow.style.transform="rotate(0deg)"},selectEndGet:function(e){this.formBase.type=e},selectDepart:function(e){this.startAgency.name=e.name},selectDepartEnd:function(e){this.endAgency.name=e.name},dialogFormV:function(){this.dialogFormVisible=!0},dialogFormH:function(){this.dialogFormVisible=!1,this.$refs["dataForm"].resetFields()},createData:function(){var e=this;this.flag&&(this.flag=!1,this.$refs["dataForm"].validate(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(r){var a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!r){t.next=11;break}if(a={number:e.formBase.number,name:e.formBase.name,cost:e.formBase.cost,distance:e.formBase.distance,startOrganName:e.startAgency.name,startOrganId:e.formBase.startAgency,endOrganId:e.formBase.endAgency,endOrganName:e.endAgency.name,type:e.formBase.type},!e.formBase.id){t.next=7;break}return t.next=5,Object(s["r"])(e.formBase.id,a).then((function(t){e.dialogFormH(),"200"===String(t.code)?(e.$message.success("线路修改成功"),e.$emit("newDataes",e.formBase)):e.$message.error(t.msg||"线路修改失败"),e.flag=!0})).catch((function(t){e.$message.error(t.msg||"线路修改失败")}));case 5:t.next=9;break;case 7:return t.next=9,Object(s["n"])(a).then((function(t){"200"===String(t.code)?(e.$message.success("新增线路成功"),e.$emit("newDataes",e.formBase),e.dialogFormH()):e.$message.error(t.msg||"线路新增失败"),e.flag=!0})).catch((function(t){e.$message.error(t.msg||"新增线路失败")}));case 9:t.next=13;break;case 11:e.flag=!0,e.$message.error("请按照提示填写!");case 13:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()))}}}),u=c,d=(r("ab1a"),r("2877")),m=Object(d["a"])(u,a,n,!1,null,"ebf15f16",null);t["default"]=m.exports},2934:function(e,t,r){"use strict";r.d(t,"e",(function(){return n})),r.d(t,"f",(function(){return i})),r.d(t,"d",(function(){return s})),r.d(t,"a",(function(){return o})),r.d(t,"b",(function(){return l}));var a=r("b775"),n=function(e){return Object(a["a"])("/web-manager/common/transportLineType/simple","get",e)},i=function(e){return Object(a["a"])("/truckType/simple","get",e)},s=function(e){return Object(a["a"])("/web-manager/common/fleet/simple","get",e)},o=function(e){return Object(a["a"])("/areas/children","get",e)},l=function(e){return Object(a["a"])("/workspace","get",e)}},"2b03":function(e,t,r){},"2da8":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("el-dialog",{attrs:{title:e.titleInfo.text,visible:e.dialogFormVisible,width:"600px","before-close":e.dialogFormH},on:{"update:visible":function(t){e.dialogFormVisible=t}}},[r("el-form",{ref:"dataForm",staticStyle:{width:"100%"},attrs:{rules:e.ruleInline,model:e.formData,"label-position":"right","label-width":"100px"}},[r("el-form-item",{attrs:{label:"线路名称：",prop:"lineName"}},[r("el-input",{attrs:{disabled:!0,placeholder:"请输入线路名称"},model:{value:e.formData.lineName,callback:function(t){e.$set(e.formData,"lineName",t)},expression:"formData.lineName"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"车次名称：",prop:"tripsName"}},[r("el-input",{attrs:{disabled:!0,placeholder:"请输入车次名称"},model:{value:e.formData.tripsName,callback:function(t){e.$set(e.formData,"tripsName",t)},expression:"formData.tripsName"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"发车时间：",prop:"departureTime"}},[r("el-input",{attrs:{disabled:!0,placeholder:"请输入发车时间"},model:{value:e.formData.departureTime,callback:function(t){e.$set(e.formData,"departureTime",t)},expression:"formData.departureTime"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"车辆安排：",prop:"truckId"}},[r("el-select",{attrs:{"suffix-icon":"el-icon-search",placeholder:"请输入车牌号",filterable:""},on:{change:e.handleChange},model:{value:e.formData.truckId,callback:function(t){e.$set(e.formData,"truckId",t)},expression:"formData.truckId"}},e._l(e.options,(function(e){return r("el-option",{key:e.id,attrs:{label:e.licensePlate,value:e.id}})})),1)],1)],1),e._v(" "),r("div",{staticClass:"dialog-footer",staticStyle:{"text-align":"center"},attrs:{slot:"footer"},slot:"footer"},[r("el-button",{staticClass:"save-btn",on:{click:e.createData}},[e._v("确认")]),e._v(" "),r("el-button",{staticClass:"cancel-btn",on:{click:e.dialogFormH}},[e._v("取消")])],1)],1)],1)},n=[],i=(r("96cf"),r("3b8d")),s=r("75fc"),o=(r("7f7f"),r("7b11")),l=r("0a86"),c=(r("542c"),{props:{titleInfo:{type:Object,required:!0},formBaseAv:{type:Object,required:!0}},data:function(){return{truckId:"",truckName:"",requestParameters:{},result:{},options:[],dialogFormVisible:!1,ruleInline:{name:[{required:!0,message:"车次不能为空",trigger:"blur"}]},formData:{truckId:""},selectedCarId:"",normalizer:function(e){return{id:e.id,label:e.name,children:e.trucks}}}},computed:{},watch:{formBaseAv:function(e,t){this.formData=e}},mounted:function(){},methods:{handleChange:function(e){this.$set(this.formData,"truckId",e),this.$forceUpdate()},resetOptions:function(e){e&&(this.options=e.truck?Object(s["a"])(this.options.concat(e.truck)):this.options)},dialogFormV:function(){this.dialogFormVisible=!0,this.getList()},dialogFormH:function(){this.dialogFormVisible=!1,this.getList()},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t,r;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(l["c"])({endAgentId:this._props.formBaseAv.endAgentId,startAgentId:this._props.formBaseAv.startAgentId});case 2:t=e.sent,r=t.data,this.options=r;case 5:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),createData:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$refs["dataForm"].validate(function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(r){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!r){e.next=6;break}return t.dialogFormH(),e.next=4,Object(o["a"])({truckIds:[t.formData.truckId],transportTripsId:t.formData.tripsId}).then((function(e){"200"===String(e.code)?(t.$message({message:"车辆安排成功!",type:"success"}),t.$emit("newDataesAv")):t.$message({message:e.msg||"车辆安排失败!",type:"error"})}));case 4:e.next=7;break;case 6:t.$message.error("*号为必填项!");case 7:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}());case 1:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()}}),u=c,d=r("2877"),m=Object(d["a"])(u,a,n,!1,null,null,null);t["default"]=m.exports},3264:function(e,t,r){"use strict";var a=r("2b03"),n=r.n(a);n.a},"51fc":function(e,t,r){},5850:function(e,t,r){"use strict";r.d(t,"a",(function(){return n})),r.d(t,"b",(function(){return i})),r.d(t,"c",(function(){return s})),r.d(t,"d",(function(){return o}));var a=r("b775"),n=function(e){return Object(a["a"])("/business-hall/tree","get",e)},i=function(e){return Object(a["a"])("/business-hall/".concat(e),"get",e)},s=function(e){return Object(a["a"])("/business-hall/user/page","get",e)},o=function(e){return Object(a["a"])("/business-hall","post",e)}},"5ae1":function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAcCAYAAAB2+A+pAAAAAXNSR0IArs4c6QAAAP1JREFUSA1jYBgFoyEwGgLDNgT+///PQonnyNJ///4riRt3H1++ce9JKDmW37j3NBmo//TDhx8Ecelnwibxm+FnGFBch+E/wzJSLQdZysjwfzZQv8mPv198sJkPEsNqsZqi7CQGRsYWBgZgcJNgOcxSYDAzMjIwFqsrySzGZTFecaBvm4FB9v/G3Se/CfkcZOnNe0/+gdTfvPukCK/BxEgSYznVLYU5DJ/lNLMUn+WUWMoIM5gYGuRzhv//axgYGP8AU888oJ5UeEJSlukjxgyYGpIsBmlCWA4xApx6SbQUZjnJNCzOqZJ6SbX9zoOnVqTqGVU/GgKjITC8QgAAQhHA2apbODgAAAAASUVORK5CYII="},6241:function(e,t,r){"use strict";var a=r("51fc"),n=r.n(a);n.a},"697a":function(e,t,r){"use strict";var a=r("8de2"),n=r.n(a);n.a},"7b11":function(e,t,r){"use strict";r.d(t,"j",(function(){return n})),r.d(t,"l",(function(){return i})),r.d(t,"e",(function(){return s})),r.d(t,"d",(function(){return o})),r.d(t,"f",(function(){return l})),r.d(t,"g",(function(){return c})),r.d(t,"h",(function(){return u})),r.d(t,"i",(function(){return d})),r.d(t,"a",(function(){return m})),r.d(t,"b",(function(){return p})),r.d(t,"z",(function(){return f})),r.d(t,"C",(function(){return g})),r.d(t,"B",(function(){return h})),r.d(t,"A",(function(){return b})),r.d(t,"y",(function(){return v})),r.d(t,"q",(function(){return y})),r.d(t,"n",(function(){return w})),r.d(t,"o",(function(){return x})),r.d(t,"p",(function(){return D})),r.d(t,"r",(function(){return k})),r.d(t,"c",(function(){return _})),r.d(t,"w",(function(){return S})),r.d(t,"t",(function(){return L})),r.d(t,"x",(function(){return T})),r.d(t,"v",(function(){return O})),r.d(t,"u",(function(){return F})),r.d(t,"m",(function(){return j})),r.d(t,"k",(function(){return $})),r.d(t,"s",(function(){return A}));var a=r("b775"),n=function(e){return Object(a["a"])("/truckType/simple","get",e)},i=function(e){return Object(a["a"])("/bindingDrivers/".concat(e),"get",e)},s=function(e){return Object(a["a"])("/driver/".concat(e),"get",e)},o=function(e,t){return Object(a["a"])("/web-manager/transfor-center/bussiness/driver/".concat(e),"put",t)},l=function(e,t){return Object(a["a"])("/driver/".concat(e),"put",t)},c=function(e){return Object(a["a"])("/driverLicense/".concat(e),"get",e)},u=function(e){return Object(a["a"])("/driverLicense","post",e)},d=function(e){return Object(a["a"])("/web-manager/transfor-center/bussiness/driver/".concat(e,"/truck"),"get",e)},m=function(e){return Object(a["a"])("/transportLine/trips/".concat(e.transportTripsId,"/truckDrivers"),"post",e)},p=function(e){return Object(a["a"])("/truck/truckDrivers","post",e)},f=function(e){return Object(a["a"])("/truck/".concat(e),"get",e)},g=function(e,t){return Object(a["a"])("/truck/".concat(e),"put",t)},h=function(e){return Object(a["a"])("/truck/".concat(e,"/license"),"get",e)},b=function(e,t){return Object(a["a"])("/truck/".concat(e,"/license"),"post",t)},v=function(e){return Object(a["a"])("/truck/".concat(e,"/transportTrips"),"get",e)},y=function(e){return Object(a["a"])("/transportLine/page","post",e)},w=function(e){return Object(a["a"])("/transportLine","post",e)},x=function(e){return Object(a["a"])("/transportLine/".concat(e),"delete",e)},D=function(e){return Object(a["a"])("/transportLine/".concat(e),"get",e)},k=function(e,t){return Object(a["a"])("/transportLine/".concat(e),"put",t)},_=function(e){return Object(a["a"])("/transportLine/trips/truckDrivers","get",e)},S=function(e){return Object(a["a"])("/transportLine/trips","get",e)},L=function(e){return Object(a["a"])("/transportLine/trips","post",e)},T=function(e,t){return Object(a["a"])("/transportLine/trips/".concat(e),"put",t)},O=function(e){return Object(a["a"])("/transportLine/trips/".concat(e),"get",e)},F=function(e){return Object(a["a"])("/transportLine/trips/".concat(e),"delete",e)},j=function(e){return Object(a["a"])("/files/imageUpload","post",e)},$=function(e){return Object(a["a"])("/cost-configuration-manager","get",e)},A=function(e){return Object(a["a"])("/cost-configuration-manager","post",e)}},"8de2":function(e,t,r){},a412:function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"line-manage"},[r("el-dialog",{attrs:{title:e.titleInfo.text,visible:e.dialogFormVisible,width:"600px"},on:{"update:visible":function(t){e.dialogFormVisible=t}}},[r("el-form",{ref:"dataForm",staticStyle:{width:"100%"},attrs:{rules:e.ruleInline,model:e.formBase1,"label-position":"right","label-width":"120px"}},[r("el-form-item",{attrs:{label:"线路名称：",prop:"lineName"}},[r("el-input",{attrs:{disabled:!0,placeholder:"请输入线路名称",maxlength:"15"},model:{value:e.transportLineName,callback:function(t){e.transportLineName=t},expression:"transportLineName"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"车次名称：",prop:"name"}},[r("el-input",{attrs:{placeholder:"请输入车次名称",maxlength:"15"},model:{value:e.formBase1.name,callback:function(t){e.$set(e.formBase1,"name",t)},expression:"formBase1.name"}})],1),e._v(" "),r("el-form-item",{staticStyle:{width:"100%"},attrs:{prop:"departureTime",label:"发车时间："}},[r("el-time-picker",{staticStyle:{width:"100%"},attrs:{"picker-options":{start:"00:00",end:"23:59"},editable:!1,format:"HH:mm",placeholder:"请选择发车时间"},on:{change:e.handleDate},model:{value:e.formBase1.departureTime,callback:function(t){e.$set(e.formBase1,"departureTime",t)},expression:"formBase1.departureTime"}})],1),e._v(" "),r("el-form-item",{staticStyle:{width:"100%"},attrs:{prop:"estimatedTime",label:"持续时间："}},[r("el-input",{attrs:{placeholder:"请输入持续时间",maxlength:"20"},model:{value:e.formBase1.estimatedTime,callback:function(t){e.$set(e.formBase1,"estimatedTime",t)},expression:"formBase1.estimatedTime"}},[r("span",{staticStyle:{color:"#20232a","font-weight":"400","font-size":"14px","font-family":"PingFangSC, PingFangSC-Regular","margin-right":"14px"},attrs:{slot:"suffix"},slot:"suffix"},[e._v("分钟")])])],1),e._v(" "),r("el-form-item",{staticStyle:{width:"100%"},attrs:{prop:"periodName",label:"发车周期："}},[r("el-select",{staticStyle:{width:"100%"},attrs:{"value-key":"period",placeholder:"请选择周期",clearable:""},on:{change:e.selectEndGet},model:{value:e.formBase1.periodName,callback:function(t){e.$set(e.formBase1,"periodName",t)},expression:"formBase1.periodName"}},e._l(e.options,(function(e){return r("el-option",{key:e.period,attrs:{value:e.period,label:e.periodName}})})),1)],1)],1),e._v(" "),r("div",{staticClass:"dialog-footer",staticStyle:{"text-align":"center"},attrs:{slot:"footer"},slot:"footer"},[r("el-button",{staticClass:"save-btn",on:{click:e.createData}},[e._v("确认")]),e._v(" "),r("el-button",{staticClass:"cancel-btn",on:{click:e.dialogFormH}},[e._v("取消")])],1)],1)],1)},n=[],i=(r("c5f6"),r("7f7f"),r("96cf"),r("3b8d")),s=r("7b11"),o={name:"LineAdd",props:{titleInfo:{type:Object,required:!0},transportLineId:{type:String,require:!0,default:""},transportLineName:{type:String,require:!0,default:""}},data:function(){return{requestTripsParameters:{transportLineId:""},options:[{period:1,periodName:"天"},{period:2,periodName:"周"},{period:3,periodName:"月"}],formBase1:{lineName:"",name:"",departureTime:"",estimatedTime:"",periodName:"",period:""},departureTime:"",periods:{period:"",periodName:""},dialogFormVisible:!1,ruleInline:{name:[{required:!0,message:"车次不能为空",trigger:"blur"}],departureTime:[{required:!0,message:"发车时间不能为空",trigger:"blur"}],estimatedTime:[{required:!0,message:"持续时间不能为空",trigger:"blur"}],periodName:[{required:!0,message:"周期不能为空",trigger:"change"}]}}},methods:{handleDate:function(e){this.departureTime=((e.getTime()-new Date(new Date((new Date).getTime()).setHours(0,0,0,0)).getTime())/1e3/60).toFixed()},hanldeEditTripsForm:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var r,a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(s["v"])(t);case 2:r=e.sent,a=r.data,this.formBase1.id=a.id,null!=a.transportLine&&(this.formBase1.lineName=a.transportLine.name,this.formBase1.lineId=a.transportLine.id),this.formBase1.name=a.name,this.formBase1.departureTime=1e3*Number(a.departureTime)*60+new Date((new Date).getTime()).setHours(0,0,0,0),this.formBase1.periodName=a.periodName,this.formBase1.period=a.period,this.formBase1.estimatedTime=a.estimatedTime;case 11:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),selectEndGet:function(e){var t=[];t=this.options.filter((function(t){return t.period===Number(e)})),this.$set(this.formBase1,"periodName",t[0].periodName),this.$set(this.formBase1,"period",e)},dialogFormV:function(){this.dialogFormVisible=!0},dialogFormH:function(){this.dialogFormVisible=!1,this.$refs["dataForm"].resetFields()},createData:function(){var e=this;this.$refs["dataForm"].validate(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(r){var a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!r){t.next=12;break}if(a={name:e.formBase1.name,departureTime:e.departureTime,period:e.formBase1.period,periodName:e.formBase1.periodName,transportLineId:e.transportLineId,estimatedTime:e.formBase1.estimatedTime},!e.formBase1.id||"新增车次"===e.titleInfo.text){t.next=7;break}return t.next=5,Object(s["x"])(e.formBase1.id,a).then((function(t){"200"===String(t.code)&&(e.$message({message:"车次编辑成功!",type:"success"}),e.$emit("newDataes1"))}));case 5:t.next=9;break;case 7:return t.next=9,Object(s["t"])(a).then((function(t){"200"===String(t.code)&&(e.$message({message:"车次添加成功!",type:"success"}),e.$emit("newDataes1"))}));case 9:e.dialogFormH(),t.next=13;break;case 12:e.$message.error("*号为必填项!");case 13:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())}}},l=o,c=(r("aacd"),r("2877")),u=Object(c["a"])(l,a,n,!1,null,"0adb6a6a",null);t["default"]=u.exports},aacd:function(e,t,r){"use strict";var a=r("13b5"),n=r.n(a);n.a},ab1a:function(e,t,r){"use strict";var a=r("c2f7"),n=r.n(a);n.a},aefe:function(e,t,r){e.exports=r.p+"static/img/pic-kong.742d3899.png"},c2f7:function(e,t,r){},c6fa:function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"dashboard-container line-manage customer-list-box"},[a("div",{staticClass:"app-container"},[a("el-card",{staticClass:"search-card-box",attrs:{shadow:"never"}},[a("el-form",{ref:"lineManageSearchFormData",attrs:{model:e.lineManageSearchFormData,"label-width":"90px"}},[a("el-row",{attrs:{gutter:60}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{staticStyle:{"margin-bottom":"0px"},attrs:{label:"线路编号:"}},[a("el-input",{attrs:{placeholder:"请输入线路编号",clearable:""},model:{value:e.lineManageSearchFormData.number,callback:function(t){e.$set(e.lineManageSearchFormData,"number",t)},expression:"lineManageSearchFormData.number"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:8}},[a("el-form-item",{staticStyle:{"margin-bottom":"0px"},attrs:{label:"线路名称:","label-width":"80px"}},[a("el-input",{attrs:{placeholder:"请输入线路名称",clearable:""},model:{value:e.lineManageSearchFormData.name,callback:function(t){e.$set(e.lineManageSearchFormData,"name",t)},expression:"lineManageSearchFormData.name"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:8}},[a("el-form-item",{staticStyle:{"margin-bottom":"20px"},attrs:{prop:"startOrganId",label:"起始机构:","label-width":"80px"}},[a("treeselect",{staticStyle:{width:"100%"},attrs:{options:e.agencyOptions,normalizer:e.normalizer,searchable:!0,placeholder:"请选择起始机构","no-options-text":"暂无数据","no-results-text":"暂无数据"},on:{select:e.selectStartAgency,open:e.open,close:e.close},model:{value:e.lineManageSearchFormData.startOrganId,callback:function(t){e.$set(e.lineManageSearchFormData,"startOrganId",t)},expression:"lineManageSearchFormData.startOrganId"}}),e._v(" "),a("img",{ref:"arrow",staticStyle:{position:"absolute",width:"18px",height:"18px",right:"9px",top:"12px",cursor:"pointer","pointer-events":"none","transition-duration":"0.3s"},attrs:{src:r("5ae1")}})],1)],1),e._v(" "),a("el-col",{attrs:{span:8}},[a("el-form-item",{staticStyle:{"margin-bottom":"0px"},attrs:{prop:"endOrganId",label:"目的地机构:"}},[a("treeselect",{staticStyle:{width:"100%"},attrs:{options:e.agencyOptions,normalizer:e.normalizer,searchable:!0,"no-options-text":"暂无数据","no-results-text":"暂无数据",placeholder:"请选择目的地机构"},on:{select:e.selectEndAgency,open:e.opens,close:e.closes},model:{value:e.lineManageSearchFormData.endOrganId,callback:function(t){e.$set(e.lineManageSearchFormData,"endOrganId",t)},expression:"lineManageSearchFormData.endOrganId"}}),e._v(" "),a("img",{ref:"arrows",staticStyle:{position:"absolute",width:"18px",height:"18px",right:"9px",top:"12px",cursor:"pointer","pointer-events":"none","transition-duration":"0.3s"},attrs:{src:r("5ae1")}})],1)],1),e._v(" "),a("el-col",{attrs:{span:8}},[a("el-button",{attrs:{type:"warning"},on:{click:e.handleFilter}},[e._v("搜索")]),e._v(" "),a("el-button",{staticClass:"reset-btn",attrs:{plain:""},on:{click:function(t){return e.resetForm("lineManageSearchFormData")}}},[e._v("重置")])],1)],1)],1)],1),e._v(" "),a("el-card",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticClass:"table-card-box",class:{"table-empty-box":!e.dataList||e.dataList.length<=0,"loading-box":e.listLoading},staticStyle:{"margin-top":"20px"},attrs:{"element-loading-text":"加载中",shadow:"never"}},[a("el-button",{staticClass:"customer-add-btn",staticStyle:{"margin-bottom":"10px"},on:{click:function(t){return e.handleAddLine()}}},[e._v("新增线路")]),e._v(" "),a("el-button",{staticClass:"customer-add-btn",staticStyle:{"margin-bottom":"10px"},on:{click:function(t){return e.handleCostSetting()}}},[e._v("成本设置")]),e._v(" "),a("el-table",{staticStyle:{width:"100%","margin-top":"10px"},attrs:{data:e.dataList,"row-key":e.getRowKeys,"expand-row-keys":e.expands,stripe:"","header-cell-style":{background:"rgba(250,252,255,1)"}},on:{"expand-change":e.expandChange,"selection-change":e.handleSelectionChange}},[a("div",{directives:[{name:"show",rawName:"v-show",value:(!e.dataList||e.dataList.length<=0)&&!e.listLoading&&!e.searchkey,expression:"\n            (!dataList || dataList.length <= 0) && !listLoading && !searchkey\n          "}],attrs:{slot:"empty"},slot:"empty"},[a("img",{staticStyle:{"margin-top":"20px",width:"25%",height:"25%"},attrs:{src:r("aefe"),alt:"img"}}),e._v(" "),a("p",{staticStyle:{"margin-top":"-10px","padding-bottom":"10px"}},[e._v("这里空空如也")])]),e._v(" "),a("el-table-column",{attrs:{type:"expand"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"children-table-box"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loadingTable,expression:"loadingTable"}],ref:"multipleTable",staticStyle:{width:"100%",margin:"auto","border-color":"#ebeef5","border-style":"solid","border-width":"1px 1px 0 1px"},attrs:{data:e.tripsData,"element-loading-text":"加载中","row-key":e.getRowKeys},on:{"selection-change":e.handleSelectionChange}},[a("div",{directives:[{name:"show",rawName:"v-show",value:(!e.tripsData||e.tripsData.length<=0)&&!e.listLoading,expression:"\n                    (!tripsData || tripsData.length <= 0) && !listLoading\n                  "}],attrs:{slot:"empty"},slot:"empty"},[a("img",{staticStyle:{"margin-top":"20px",width:"25%",height:"25%"},attrs:{src:r("aefe"),alt:"img"}}),e._v(" "),a("p",{staticStyle:{"margin-top":"-10px","padding-bottom":"10px"}},[e._v("\n                    这里空空如也\n                  ")])]),e._v(" "),a("el-table-column",{attrs:{prop:"name",label:"车次名称"}}),e._v(" "),a("el-table-column",{attrs:{prop:"departureTime",label:"发车频次"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.handleDates(t.row.departureTime))+" /\n                      "+e._s(t.row.periodName))])]}}],null,!0)}),e._v(" "),a("el-table-column",{attrs:{prop:"arrivalTime",label:"到达时间"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.handleDates(t.row.arriveTime)))])]}}],null,!0)}),e._v(" "),a("el-table-column",{attrs:{prop:"legal",label:"车辆安排"},scopedSlots:e._u([{key:"default",fn:function(t){return t.row.truckDrivers?e._l([].concat(t.row.truckDrivers.map((function(e){return e.truck.licensePlate})).filter((function(e,r){return t.row.truckDrivers.map((function(e){return e.truck.licensePlate})).indexOf(e)===r}))),(function(t){return a("span",{key:t},[e._v("\n                      "+e._s(t)),a("br")])})):void 0}}],null,!0)}),e._v(" "),a("el-table-column",{attrs:{prop:"legal",label:"司机安排"},scopedSlots:e._u([{key:"default",fn:function(t){return e._l(t.row.truckDrivers,(function(t,r){return a("span",{key:t.id},[e._v("\n                      "+e._s(null===t.driver?"":t.driver.name)+"，"),r%2!==0?a("br"):e._e()])}))}}],null,!0)}),e._v(" "),a("el-table-column",{attrs:{prop:"cat_id",label:"操作",fixed:"right",width:"314",align:"center"},scopedSlots:e._u([{key:"default",fn:function(r){return[a("el-button",{attrs:{type:"text",size:"mini"},on:{click:function(a){return e.handleEditTrips(r.row.id,t.row.name)}}},[e._v("编辑车次")]),e._v(" "),a("el-divider",{attrs:{direction:"vertical"}}),e._v(" "),a("el-button",{staticStyle:{color:"rgba(245, 108, 108, 1)"},attrs:{type:"text",size:"small"},on:{click:function(t){return e.handleDelTrips(r.row.id)}}},[e._v("删除车次")]),e._v(" "),a("el-divider",{attrs:{direction:"vertical"}}),e._v(" "),a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return e.arrangeVehicles(t.row.id,t.row.name,r.row.id,r.row.name,r.row.departureTime,r.row.arrivalTime,r.row.truckDrivers?r.row.truckDrivers[0]:{},r.row.transportLine)}}},[e._v("安排车辆")]),e._v(" "),a("el-divider",{attrs:{direction:"vertical"}}),e._v(" "),a("el-button",{staticStyle:{color:"rgba(245, 108, 108, 1)"},attrs:{type:"text",size:"small"},on:{click:function(t){return e.handleCarUnbind(r.row.id,r.row.truckDrivers)}}},[e._v("车辆解绑")])]}}],null,!0)})],1),e._v(" "),a("empty",{directives:[{name:"show",rawName:"v-show",value:(!e.dataList||e.dataList.length<=0)&&!e.listLoading,expression:"(!dataList || dataList.length <= 0) && !listLoading"}]})],1)]}}])}),e._v(" "),a("el-table-column",{attrs:{width:"170",prop:"number",label:"线路编号"}}),e._v(" "),a("el-table-column",{attrs:{width:"170",prop:"name",label:"线路名称"}}),e._v(" "),a("el-table-column",{attrs:{width:"170",label:"线路类型"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[a("span",[e._v(e._s(1===r.type?"干线":2===r.type?"支线":3===r.type?"接驳路线":""))])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"spu_desc","min-width":"120",label:"起始地机构"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[a("span",[e._v(e._s(r.startOrganName||""))])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"pay_type","min-width":"120",label:"目的地机构"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[a("span",[e._v(e._s(r.endOrganName||""))])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"distance",width:"120",label:"距离（千米）"}}),e._v(" "),a("el-table-column",{attrs:{prop:"cost",width:"130",label:"成本（元）"}}),e._v(" "),a("el-table-column",{attrs:{prop:"time",width:"140",label:"预计时间（分钟）"}}),e._v(" "),a("el-table-column",{attrs:{prop:"pay",label:"操作",width:"184",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(r){return e.handleAddLine(t.row.id)}}},[e._v("修改")]),e._v(" "),a("el-divider",{attrs:{direction:"vertical"}}),e._v(" "),a("el-link",{attrs:{type:"danger",underline:!1},on:{click:function(r){return e.handleDeleteLine(t.row.id)}}},[e._v("删除")]),e._v(" "),a("el-divider",{attrs:{direction:"vertical"}}),e._v(" "),a("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(r){return e.handleAddTrips(t.row.id,t.row.name)}}},[e._v("增加车次")])]}}])}),e._v(" "),a("el-card",{directives:[{name:"show",rawName:"v-show",value:(!e.dataList||e.dataList.length<=0)&&!e.listLoading&&e.searchkey,expression:"\n            (!dataList || dataList.length <= 0) && !listLoading && searchkey\n          "}],attrs:{slot:"empty",shadow:"never"},slot:"empty"},[a("empty",{staticStyle:{"text-align":"center"}})],1)],1),e._v(" "),a("div",{directives:[{name:"show",rawName:"v-show",value:e.dataList&&e.dataList.length>0,expression:"dataList && dataList.length > 0"}],staticClass:"pagination"},[a("div",{staticClass:"pages"},[a("el-pagination",{attrs:{"current-page":Number(e.lineManageSearchFormData.page),total:Number(e.total),"page-size":Number(e.lineManageSearchFormData.pageSize),"page-sizes":[10,20,30,50],layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)]),e._v(" "),a("lineDialog",{ref:"addLine",staticClass:"add-form-dialog",attrs:{"title-info":e.titleInfo,"form-base":e.formData,"agency-options":e.agencyOptions},on:{newDataes:e.getListDia}}),e._v(" "),a("costSettingDialog",{ref:"costSetting",staticClass:"add-form-dialog",attrs:{"form-base":e.costSetting},on:{resetCostSetting:e.getCostSetting}}),e._v(" "),a("tripsDialog",{ref:"addTrips",staticClass:"add-form-dialog",attrs:{"transport-line-name":e.transportLineName,"transport-line-id":e.transportLineId,"title-info":e.titleInfo},on:{newDataes1:e.expandChangeDia}}),e._v(" "),a("arrangeVehiclesDialog",{ref:"arrangeVehicles",staticClass:"add-form-dialog",attrs:{"title-info":e.titleInfo,"form-base-av":e.formDataAv},on:{newDataesAv:e.getListDia}})],1)],1),e._v(" "),a("el-dialog",{staticClass:"customer-dialog",attrs:{title:"删除确认",visible:e.dialogVisibleTrainNumber,width:"394px","before-close":e.handleCloseTrainNumber},on:{"update:visible":function(t){e.dialogVisibleTrainNumber=t}}},[a("img",{staticClass:"warn-img",attrs:{src:r("57bd"),alt:""}}),e._v(" "),a("p",[e._v("确认删除车次？")]),e._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{staticClass:"confirm-btn",attrs:{type:"primary"},on:{click:e.sumbitDelteTrainNumber}},[e._v("确定")]),e._v(" "),a("el-button",{staticClass:"cancel-btn",on:{click:function(t){e.dialogVisibleTrainNumber=!1}}},[e._v("取消")])],1)])],1)},n=[],i=(r("96cf"),r("3b8d")),s=(r("7f7f"),r("7b11")),o=r("5850"),l=r("2934"),c=r("2765"),u=r("f49c"),d=r("a412"),m=r("2da8"),p=r("ca17"),f=r.n(p),g=r("ed08"),h={name:"LineManage",components:{costSettingDialog:u["default"],lineDialog:c["default"],tripsDialog:d["default"],arrangeVehiclesDialog:m["default"],Treeselect:f.a},data:function(){return{normalizer:function(e){return{id:e.id,label:e.name,children:e.children}},transportLineName:"",transportLineId:"",dialogVisible:!1,handleDeleteId:"",dialogVisibleTrainNumber:!1,handleDeleteIdTrainNumber:"",dialogShow:!1,wholeTitle:"地图线路展示",expands:[],getRowKeys:function(e){return e.id},agencyOptions:[],trips:[],multipleSelection:[],options:[{id:1,name:"干线"},{id:2,name:"支线"},{id:3,name:"接驳路线"}],lineType:"",tableKey:0,tableKey1:0,loadingTable:!1,tripsData:[],dataList:[],tripsDataList:[],total:null,listLoading:!0,searchkey:!1,alertText:"",lineManageSearchFormData:{page:1,pageSize:10,number:"",name:"",transportLineTypeId:"",startOrganId:null,endOrganId:null},requestParameters1:{transportLineId:""},titleInfo:{pageTitle:"",text:""},formData:{number:"XL",lineName:"",name:"",lineNumber:"",distance:"",cost:"",estimatedTime:"",lineCommonName:"",startAgency:null,endAgency:null,type:""},formData1:{name:"",lineId:"",lineName:"",period:"",periodName:"",departureTime:""},formDataAv:{},formDataAd:{},costSetting:{transportLineType1:"",transportLineType2:"",transportLineType3:""}}},created:function(){var e=this;this.initialDate(),document.onkeydown=function(t){var r=window.event.keyCode;13===r&&e.handleFilter(this.lineManageSearchFormData)}},methods:{opens:function(){this.$refs.arrows.style.transform="rotate(-180deg)"},closes:function(){this.$refs.arrows.style.transform="rotate(0deg)"},open:function(){this.$refs.arrow.style.transform="rotate(-180deg)"},close:function(){this.$refs.arrow.style.transform="rotate(0deg)"},handleDates:function(e){return Object(g["c"])(e)},selectEndAgency:function(e){this.lineManageSearchFormData.endOrganId=e.id},selectStartAgency:function(e){this.lineManageSearchFormData.startOrganId=e.id},getCostSetting:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t,r,a=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(s["k"])();case 2:t=e.sent,r=t.data,r.map((function(e){1===e.transportLineType?a.costSetting["transportLineType1"]=e.cost:2===e.transportLineType?a.costSetting["transportLineType2"]=e.cost:a.costSetting["transportLineType3"]=e.cost}));case 5:case"end":return e.stop()}}),e)})));function t(){return e.apply(this,arguments)}return t}(),getAgencyList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t,r;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(o["a"])();case 2:t=e.sent,r=t.data,this.agencyOptions=JSON.parse(r);case 5:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),handleClose:function(){this.dialogVisible=!1},handleCloseTrainNumber:function(){this.dialogVisibleTrainNumber=!1},closeDialog:function(){this.dialogShow=!1},initialDate:function(){this.clearField(),this.getList(),this.getAgencyList(),this.getCostSetting()},clearField:function(){""===this.lineManageSearchFormData.number&&this.$delete(this.lineManageSearchFormData,"number"),""===this.lineManageSearchFormData.transportLineTypeId&&this.$delete(this.lineManageSearchFormData,"transportLineTypeId"),""===this.lineManageSearchFormData.name&&this.$delete(this.lineManageSearchFormData,"name")},setField:function(){this.$set(this.lineManageSearchFormData,"number"),this.$set(this.lineManageSearchFormData,"transportLineTypeId"),this.$set(this.lineManageSearchFormData,"name")},expandChange:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t,r){var a,n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.tripsData=[],this.loadingTable=!0,!r.length){e.next=15;break}return this.expands=[],t&&this.expands.push(t.id),this.transportLineId=t.id,this.requestParameters1.transportLineId=t.id,e.next=9,Object(s["w"])(this.requestParameters1);case 9:a=e.sent,n=a.data,this.tripsData=n||[],this.loadingTable=!1,e.next=16;break;case 15:this.expands=[];case 16:case"end":return e.stop()}}),e,this)})));function t(t,r){return e.apply(this,arguments)}return t}(),expandChangeDia:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t,r;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(s["w"])(this.requestParameters1);case 2:t=e.sent,r=t.data,this.tripsData=r,this.loadingTable=!1;case 6:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),handleSelectionChange:function(e){this.multipleSelection=e},getLineList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t,r;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(l["e"])();case 2:t=e.sent,r=t.data,this.options=r.filter((function(e){return 1===e.status}));case 5:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),getListDia:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t,r;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.getList(),this.formData=Object.assign({},this.formData,{number:"XL",type:""}),e.next=4,Object(s["w"])(this.requestParameters1);case 4:t=e.sent,r=t.data,this.tripsData=r,this.loadingTable=!1;case 8:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t,r;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.listLoading=!0,e.next=3,Object(s["q"])(this.lineManageSearchFormData);case 3:t=e.sent,r=t.data,this.listLoading=!1,this.dataList=r.items,this.total=r.counts;case 8:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),resetForm:function(e){this.setField(),this.searchkey=!1,this.$refs[e].resetFields(),this.getList()},handleFilter:function(){this.clearField(),this.lineManageSearchFormData.page=1,this.getList(this.lineManageSearchFormData),this.searchkey=!0},handleSizeChange:function(e){this.lineManageSearchFormData.pageSize=e,1===this.lineManageSearchFormData.page&&this.getList(this.lineManageSearchFormData)},handleCurrentChange:function(e){this.lineManageSearchFormData.page=e,this.getList()},arrangeVehicles:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t,r,a,n,i,s,o,l){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.titleInfo.text="安排车辆",this.$set(this.formDataAv,"tripsId",a),this.formDataAv.lineId=t,this.formDataAv.lineName=r,this.formDataAv.tripsId=a,this.formDataAv.tripsName=n,this.formDataAv.departureTime=this.handleDates(i),this.formDataAv.arrivalTime=s,this.formDataAv.truckDrivers=o,this.formDataAv.truckId=o.truck?o.truck.id:"",this.formDataAv.endAgentId=l.endOrganId,this.formDataAv.startAgentId=l.startOrganId,this.$refs.arrangeVehicles.dialogFormV(),this.$refs.arrangeVehicles.resetOptions(o);case 14:case"end":return e.stop()}}),e,this)})));function t(t,r,a,n,i,s,o,l){return e.apply(this,arguments)}return t}(),handleAddTrips:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t,r){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$refs.addTrips.dialogFormV(),this.transportLineId=t,this.transportLineName=r,this.titleInfo.text="新增车次";case 4:case"end":return e.stop()}}),e,this)})));function t(t,r){return e.apply(this,arguments)}return t}(),handleEditTrips:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t,r){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$refs.addTrips.dialogFormV(),this.$refs.addTrips.hanldeEditTripsForm(t),this.titleInfo.text="编辑车次",this.transportLineName=r;case 4:case"end":return e.stop()}}),e,this)})));function t(t,r){return e.apply(this,arguments)}return t}(),handleCostSetting:function(){this.$refs.costSetting.dialogFormV()},handleAddLine:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:t?(this.hanldeEditForm(t),this.titleInfo.text="编辑线路"):(this.$refs.addLine.dialogFormV(),this.titleInfo.text="新增线路",this.formData.lineName="",this.formData.name="",this.formData.lineNumber="",this.formData.distance="",this.formData.cost="",this.formData.estimatedTime="",this.formData.lineCommonName="",this.formData.startAgency=null,this.formData.endAgency=null);case 1:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),hanldeEditForm:function(e){var t=this;Object(s["p"])(e).then((function(e){var r=e.data;t.formData.number=r.number,t.formData.name=r.name,t.formData.type=r.type,t.formData.startAgency=r.startOrganId,t.formData.endAgency=r.endOrganId,t.formData.distance=r.distance,t.formData.cost=r.cost,t.formData.id=r.id,t.$refs.addLine.dialogFormV()}))},handleCarUnbind:function(e,t){var r=this;if(null==t)return this.$alert("此车次下无绑定车辆，无法解绑！","解绑失败",{confirmButtonText:"确定"});this.$confirm("确定要解绑当前车次下的全部车辆吗？","解绑确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Object(s["a"])({transportTripsId:e}).then((function(e){r.$refs.arrangeVehicles.getList(),"200"===String(e.code)?(r.$message({message:"车辆解绑成功!",type:"success"}),r.getReflaushFn(),r.getList()):r.$alert("此车次下无绑定车辆，无法解绑！","解绑失败",{confirmButtonText:"确定"})}))})).catch((function(){r.$message({type:"info",message:"已取消"})}))},handleDeleteLine:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var r=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.handleDeleteId="",this.handleDeleteId=t,this.$confirm("确定要删除线路吗?","删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){r.change(t)})).catch((function(){r.$message({type:"info",message:"已取消"})}));case 3:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),sumbitDelte:function(){this.change(this.handleDeleteId)},change:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var r=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(s["o"])(t).then((function(e){"200"===String(e.code)?(r.$message.success("删除成功"),r.getList(),r.dialogVisible=!1):r.$message.error(e.msg||"删除失败")}));case 2:case"end":return e.stop()}}),e)})));function t(t){return e.apply(this,arguments)}return t}(),handleDelTrips:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var r=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.handleDeleteIdTrainNumber="",this.handleDeleteIdTrainNumber=t,this.$confirm("确定要删除车次吗?","删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){r.changeTrips(r.handleDeleteIdTrainNumber)})).catch((function(){r.$message({type:"info",message:"已取消"})}));case 3:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),sumbitDelteTrainNumber:function(){this.changeTrips(this.handleDeleteIdTrainNumber)},getReflaushFn:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t,r;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(s["w"])(this.requestParameters1);case 2:t=e.sent,r=t.data,this.tripsData=r;case 5:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),changeTrips:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var r=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,Object(s["u"])(t).then((function(e){"200"===String(e.code)?(r.$message.success("删除车次成功"),r.getReflaushFn(),r.getList(),r.dialogVisibleTrainNumber=!1):r.$message.error(e.msg||"删除车次失败")}));case 2:case"end":return e.stop()}}),e)})));function t(t){return e.apply(this,arguments)}return t}()}},b=h,v=(r("697a"),r("3264"),r("2877")),y=Object(v["a"])(b,a,n,!1,null,"6cf8f9d3",null);t["default"]=y.exports},f49c:function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("el-dialog",{attrs:{title:"成本设置",visible:e.dialogFormVisible,width:"600px"},on:{"update:visible":function(t){e.dialogFormVisible=t}}},[r("el-form",{ref:"dataForm",staticStyle:{width:"100%"},attrs:{rules:e.ruleInline,model:e.formBase,"label-position":"right","label-width":"110px"}},[r("el-form-item",{staticClass:"title",attrs:{label:"线路类型"}},[r("span",[e._v("每公里平均成本（元）")])]),e._v(" "),r("el-form-item",{attrs:{label:"干线：",prop:"transportLineType1"}},[r("el-input",{attrs:{placeholder:"请输入",maxlength:"8"},model:{value:e.formBase.transportLineType1,callback:function(t){e.$set(e.formBase,"transportLineType1",t)},expression:"formBase.transportLineType1"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"支线：",prop:"transportLineType2"}},[r("el-input",{attrs:{placeholder:"请输入",maxlength:"15"},model:{value:e.formBase.transportLineType2,callback:function(t){e.$set(e.formBase,"transportLineType2",t)},expression:"formBase.transportLineType2"}})],1),e._v(" "),r("el-form-item",{attrs:{label:"接驳：",prop:"transportLineType3"}},[r("el-input",{attrs:{placeholder:"请输入",maxlength:"15"},model:{value:e.formBase.transportLineType3,callback:function(t){e.$set(e.formBase,"transportLineType3",t)},expression:"formBase.transportLineType3"}})],1)],1),e._v(" "),r("div",{staticClass:"dialog-footer",staticStyle:{"text-align":"center"},attrs:{slot:"footer"},slot:"footer"},[r("el-button",{staticClass:"save-btn",on:{click:e.createData}},[e._v("确认")]),e._v(" "),r("el-button",{staticClass:"cancel-btn",on:{click:e.dialogFormH}},[e._v("取消")])],1)],1)],1)},n=[],i=(r("c5f6"),r("96cf"),r("3b8d")),s=r("7b11"),o={props:{formBase:{type:Object,required:!0}},data:function(){return{dialogFormVisible:!1,ruleInline:{transportLineType1:[{required:!0,validator:function(e,t,r){if(t){var a=/^[0-9]+(.[0-9]{1,2})?$/;a.test(t)?r():r(new Error("只能输入数字类型，最多保留两位小数，请重新输入"))}else r(new Error("干线不能为空"))},trigger:"blur"}],transportLineType2:[{required:!0,validator:function(e,t,r){if(t){var a=/^[0-9]+(.[0-9]{1,2})?$/;a.test(t)?r():r(new Error("只能输入数字类型，最多保留两位小数，请重新输入"))}else r(new Error("支线不能为空"))},trigger:"blur"}],transportLineType3:[{required:!0,validator:function(e,t,r){if(t){var a=/^[0-9]+(.[0-9]{1,2})?$/;a.test(t)?r():r(new Error("只能输入数字类型，最多保留两位小数，请重新输入"))}else r(new Error("接驳不能为空"))},trigger:"blur"}]},flag:!0}},created:function(){},mounted:function(){},methods:{dialogFormV:function(){this.dialogFormVisible=!0},dialogFormH:function(){this.dialogFormVisible=!1,this.$refs["dataForm"].resetFields()},createData:function(){var e=this;this.flag&&(this.flag=!1,this.$refs["dataForm"].validate(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(r){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!r){t.next=5;break}return t.next=3,Object(s["s"])([{transportLineType:3,cost:Number(e.formBase.transportLineType3)},{transportLineType:2,cost:Number(e.formBase.transportLineType2)},{transportLineType:1,cost:Number(e.formBase.transportLineType1)}]).then((function(t){e.dialogFormH(),"200"===String(t.code)?(e.$message.success("成本修改成功"),e.$emit("resetCostSetting")):e.$message.error(t.msg||"成本修改失败"),e.flag=!0})).catch((function(t){e.$message.error(t.msg||"成本修改失败"),e.flag=!0}));case 3:t.next=7;break;case 5:e.flag=!0,e.$message.error("请按照提示填写!");case 7:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()))}}},l=o,c=(r("6241"),r("2877")),u=Object(c["a"])(l,a,n,!1,null,"76707616",null);t["default"]=u.exports}}]);