(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1fd9d8a3"],{"2a58":function(t,e,a){"use strict";var n=a("a192"),r=a.n(n);r.a},5850:function(t,e,a){"use strict";a.d(e,"a",(function(){return r})),a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return s})),a.d(e,"d",(function(){return o}));var n=a("b775"),r=function(t){return Object(n["a"])("/business-hall/tree","get",t)},i=function(t){return Object(n["a"])("/business-hall/".concat(t),"get",t)},s=function(t){return Object(n["a"])("/business-hall/user/page","get",t)},o=function(t){return Object(n["a"])("/business-hall","post",t)}},"5ae1":function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAcCAYAAAB2+A+pAAAAAXNSR0IArs4c6QAAAP1JREFUSA1jYBgFoyEwGgLDNgT+///PQonnyNJ///4riRt3H1++ce9JKDmW37j3NBmo//TDhx8Ecelnwibxm+FnGFBch+E/wzJSLQdZysjwfzZQv8mPv198sJkPEsNqsZqi7CQGRsYWBgZgcJNgOcxSYDAzMjIwFqsrySzGZTFecaBvm4FB9v/G3Se/CfkcZOnNe0/+gdTfvPukCK/BxEgSYznVLYU5DJ/lNLMUn+WUWMoIM5gYGuRzhv//axgYGP8AU888oJ5UeEJSlukjxgyYGpIsBmlCWA4xApx6SbQUZjnJNCzOqZJ6SbX9zoOnVqTqGVU/GgKjITC8QgAAQhHA2apbODgAAAAASUVORK5CYII="},6134:function(t,e,a){"use strict";var n=a("9043"),r=a.n(n);r.a},9043:function(t,e,a){},"95e9":function(t,e,a){"use strict";a.d(e,"l",(function(){return r})),a.d(e,"i",(function(){return i})),a.d(e,"j",(function(){return s})),a.d(e,"k",(function(){return o})),a.d(e,"m",(function(){return c})),a.d(e,"e",(function(){return l})),a.d(e,"d",(function(){return u})),a.d(e,"f",(function(){return g})),a.d(e,"c",(function(){return p})),a.d(e,"n",(function(){return d})),a.d(e,"g",(function(){return h})),a.d(e,"b",(function(){return m})),a.d(e,"a",(function(){return f})),a.d(e,"h",(function(){return b}));var n=a("b775"),r=function(t){return Object(n["a"])("/goodsType/page","get",t)},i=function(t){return Object(n["a"])("/goodsType","post",t)},s=function(t){return Object(n["a"])("/goodsType/".concat(t),"delete",t)},o=function(t){return Object(n["a"])("/goodsType/".concat(t),"get",t)},c=function(t,e){return Object(n["a"])("/goodsType/".concat(t),"put",e)},l=function(t){return Object(n["a"])("/business-hall/courier/page","get",t)},u=function(t){return Object(n["a"])("/business-hall/courier/".concat(t),"get",t)},g=function(t,e){return Object(n["a"])("/business-hall/scope/".concat(t,"/").concat(e),"get",t)},p=function(t){return Object(n["a"])("/business-hall/scope","post",t)},d=function(t){return Object(n["a"])("/pickup-dispatch-task-manager/page","post",t)},h=function(t,e){return Object(n["a"])("/business-hall/scope/".concat(t,"/").concat(e),"delete")},m=function(t){return Object(n["a"])("/truck-return-register/pageQuery","post",t)},f=function(t){return Object(n["a"])("/truck-return-register/detail/".concat(t),"get",t)},b=function(t){return Object(n["a"])("/pickup-dispatch-task-manager/".concat(t.courierId),"put",t.ids)}},a192:function(t,e,a){},a586:function(t,e,a){t.exports=a.p+"static/img/<EMAIL>"},ca1a:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"dashboard-container operational-range customer-list-box"},[n("div",{staticClass:"app-container"},[n("el-card",{staticClass:"search-card-box",attrs:{shadow:"never"}},[n("el-form",{ref:"rangeSearchFormData",attrs:{model:t.rangeSearchFormData,"label-width":"95px"}},[n("el-row",{attrs:{gutter:60}},[n("el-col",{attrs:{span:8}},[n("el-form-item",{staticStyle:{"margin-bottom":"0px"},attrs:{label:"快递员账号:"}},[n("el-input",{attrs:{clearable:"",placeholder:"请输入快递员账号"},model:{value:t.rangeSearchFormData.account,callback:function(e){t.$set(t.rangeSearchFormData,"account",e)},expression:"rangeSearchFormData.account"}})],1)],1),t._v(" "),n("el-col",{attrs:{span:8}},[n("el-form-item",{staticStyle:{"margin-bottom":"0px"},attrs:{label:"快递员姓名:"}},[n("el-input",{attrs:{clearable:"",placeholder:"请输入快递员姓名"},model:{value:t.rangeSearchFormData.name,callback:function(e){t.$set(t.rangeSearchFormData,"name",e)},expression:"rangeSearchFormData.name"}})],1)],1),t._v(" "),n("el-col",{attrs:{span:8}},[n("el-form-item",{staticStyle:{"margin-bottom":"0px"},attrs:{label:"快递员手机号:","label-width":"110px"}},[n("el-input",{attrs:{clearable:"",placeholder:"请输入快递员手机"},model:{value:t.rangeSearchFormData.phone,callback:function(e){t.$set(t.rangeSearchFormData,"phone",e)},expression:"rangeSearchFormData.phone"}})],1)],1),t._v(" "),n("el-col",{attrs:{span:8}},[n("el-form-item",{staticStyle:{"margin-top":"20px","margin-bottom":"0px"},attrs:{prop:"username",label:"所属机构:"}},[n("treeselect",{staticStyle:{width:"100%"},attrs:{options:t.agencyOptions,normalizer:t.normalizer,searchable:!0,placeholder:"请选择所属机构","no-options-text":"暂无数据","no-results-text":"暂无数据"},on:{select:t.selectAgency,open:t.open,close:t.close},model:{value:t.rangeSearchFormData.agentId,callback:function(e){t.$set(t.rangeSearchFormData,"agentId",e)},expression:"rangeSearchFormData.agentId"}}),t._v(" "),n("img",{ref:"arrow",staticStyle:{position:"absolute",width:"18px",height:"18px",right:"9px",top:"12px",cursor:"pointer","pointer-events":"none","transition-duration":"0.3s"},attrs:{src:a("5ae1")}})],1)],1),t._v(" "),n("el-col",{staticStyle:{"margin-top":"20px"},attrs:{span:8}},[n("el-button",{attrs:{type:"warning"},on:{click:function(e){return t.handleFilter("查询")}}},[t._v("搜索")]),t._v(" "),n("el-button",{staticClass:"reset-btn",attrs:{plain:""},on:{click:function(e){return t.resetForm("rangeSearchFormData")}}},[t._v("重置")])],1)],1)],1)],1),t._v(" "),n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],class:{"loading-box":t.listLoading},staticStyle:{"margin-top":"20px"},attrs:{"element-loading-text":"加载中"}},[n("el-card",{staticClass:"table-card-box",attrs:{shadow:"never"}},[n("el-table",{key:t.tableKey,ref:"multipleTable",staticStyle:{width:"100%"},attrs:{data:t.dataList,stripe:"","header-cell-style":{background:"rgba(250,252,255,1)"}}},[n("div",{directives:[{name:"show",rawName:"v-show",value:(!t.dataList||t.dataList.length<=0)&&!t.listLoading&&!t.searchkey,expression:"(!dataList || dataList.length <= 0) && !listLoading && !searchkey"}],attrs:{slot:"empty"},slot:"empty"},[n("img",{staticStyle:{"margin-top":"20px",width:"25%",height:"25%"},attrs:{src:a("a586"),alt:"img"}}),t._v(" "),n("p",{staticStyle:{"margin-top":"-20px","padding-bottom":"0px"}},[t._v("这里空空如也")])]),t._v(" "),n("el-card",{directives:[{name:"show",rawName:"v-show",value:(!t.dataList||t.dataList.length<=0)&&!t.listLoading&&t.searchkey,expression:"(!dataList || dataList.length <= 0) && !listLoading && searchkey"}],staticClass:"table-empty-box",attrs:{slot:"empty",shadow:"never"},slot:"empty"},[n("empty")],1),t._v(" "),n("el-table-column",{attrs:{align:"left",type:"index",label:"序号",width:"100px"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(e.$index+(t.rangeSearchFormData.page-1)*t.rangeSearchFormData.pageSize+1))])]}}])}),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"快递员账号","min-width":"200px"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(e.row.account))])]}}])}),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"快递员姓名","min-width":"300px"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(e.row.name))])]}}])}),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"快递员手机号","min-width":"250px"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(e.row.mobile))])]}}])}),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"所属机构","min-width":"300px"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(null===e.row.agency?"":e.row.agency.name))])]}}])}),t._v(" "),n("el-table-column",{attrs:{align:"center",label:"工作状态","min-width":"230px"},scopedSlots:t._u([{key:"default",fn:function(e){return[null!==e.row.workStatus?n("div",{staticStyle:{width:"100%",align:"center"}},[n("span",{staticClass:"tableColumn-status",class:{"stop-use":"0"===String(e.row.workStatus)},staticStyle:{display:"inline-block",width:"100%","text-align":"center"}},[n("span",[t._v("\n                    "+t._s("1"===String(e.row.workStatus)?"上班":"休息")+"\n                  ")])])]):n("div",[t._v("未排班")])]}}])}),t._v(" "),n("el-table-column",{attrs:{align:"center",label:"操作",width:"120px","class-name":"small-padding fixed-width",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(a){return t.handleAssignmentJob(e.row)}}},[t._v("作业范围分配")])]}}])})],1),t._v(" "),n("div",{staticClass:"pagination"},[n("div",{directives:[{name:"show",rawName:"v-show",value:t.dataList&&t.dataList.length>0,expression:"dataList && dataList.length > 0"}],staticClass:"pages"},[n("el-pagination",{attrs:{"current-page":Number(t.rangeSearchFormData.page),total:Number(t.total),"page-size":Number(t.rangeSearchFormData.pageSize),"page-sizes":[10,20,30,50],layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)])],1)],1)],1)])},r=[],i=(a("96cf"),a("3b8d")),s=(a("6b54"),a("7f7f"),a("95e9")),o=a("ca17"),c=a.n(o),l=(a("542c"),a("5850")),u={name:"Courses",components:{Treeselect:c.a},data:function(){return{normalizer:function(t){return{id:t.id,label:t.name,children:t.children}},tableKey:0,dataList:[],searchkey:!1,total:null,listLoading:!0,alertText:"",rangeSearchFormData:{page:1,pageSize:10,name:"",phone:"",account:"",agentId:null},titleInfo:{pageTitle:"",text:""},formData:{userId:"",options:"",countrys:""},agencyOptions:[]}},computed:{status:function(t){function e(){return t.apply(this,arguments)}return e.toString=function(){return t.toString()},e}((function(){return status}))},mounted:function(){},created:function(){var t=this;this.companyId=this.$route.query.companyId,this.initialDate(),document.onkeydown=function(e){var a=window.event.keyCode;13===a&&t.handleFilter(this.rangeSearchFormData)}},updated:function(){},methods:{open:function(){this.$refs.arrow.style.transform="rotate(-180deg)"},close:function(){this.$refs.arrow.style.transform="rotate(0deg)"},initialDate:function(){this.getList(),this.getAgencyList()},getAgencyList:function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(){var e,a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(l["a"])();case 2:e=t.sent,a=e.data,this.agencyOptions=JSON.parse(a);case 5:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),selectAgency:function(t){this.rangeSearchFormData.agentId=t.id},getList:function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(){var e,a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return this.listLoading=!0,t.next=3,Object(s["e"])(this.rangeSearchFormData);case 3:e=t.sent,a=e.data,this.listLoading=!1,this.dataList=a.items,this.total=a.counts;case 8:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),resetForm:function(t){this.$refs[t].resetFields(),this.rangeSearchFormData.name="",this.rangeSearchFormData.phone="",this.rangeSearchFormData.account="",this.rangeSearchFormData.agentId=null,this.searchkey=!1,this.getList()},handleFilter:function(){this.rangeSearchFormData.page=1,this.getList(this.rangeSearchFormData),this.searchkey=!0},handleSizeChange:function(t){this.rangeSearchFormData.pageSize=t,1===this.rangeSearchFormData.page&&this.getList(this.rangeSearchFormData)},handleCurrentChange:function(t){this.rangeSearchFormData.page=t,this.getList()},handleAssignmentJob:function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(e){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:this.$router.push({path:"/branches/MapContent",query:{agencyName:e.agency.name,id:e.userId,name:"row.name"}});case 1:case"end":return t.stop()}}),t,this)})));function e(e){return t.apply(this,arguments)}return e}()}},g=u,p=(a("2a58"),a("6134"),a("2877")),d=Object(p["a"])(g,n,r,!1,null,"0101b080",null);e["default"]=d.exports}}]);