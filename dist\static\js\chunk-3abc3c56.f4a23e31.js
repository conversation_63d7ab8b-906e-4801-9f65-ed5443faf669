(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3abc3c56"],{3585:function(t,a,s){"use strict";s.r(a);var e=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"dashboard-container"},[e("div",{staticClass:"app-container"},[e("div",{staticClass:"item"},[e("el-container",{staticClass:"customer-details-box base-info"},[e("el-card",{staticStyle:{width:"100%"},attrs:{shadow:"never"}},[e("div",{staticClass:"block"},[e("div",{staticClass:"car-base task-info base-info"},[t._v("基本信息")]),t._v(" "),e("el-card",{staticClass:"form-box",attrs:{shadow:"never"}},[e("div",{staticClass:"form-item-box"},[e("div",{staticClass:"form-item"},[e("span",{staticClass:"span-title"},[t._v("运输任务编号：")]),t._v(" "),e("el-input",{model:{value:t.taskInfo.transportTaskId,callback:function(a){t.$set(t.taskInfo,"transportTaskId",a)},expression:"taskInfo.transportTaskId"}})],1),t._v(" "),e("div",{staticClass:"form-item"},[e("span",{staticClass:"span-title"},[t._v("车牌号码：")]),t._v(" "),e("el-input",{model:{value:t.taskInfo.licensePlate,callback:function(a){t.$set(t.taskInfo,"licensePlate",a)},expression:"taskInfo.licensePlate"}})],1),t._v(" "),e("div",{staticClass:"form-item"},[e("span",{staticClass:"span-title"},[t._v("起始地机构：")]),t._v(" "),e("el-input",{model:{value:t.taskInfo.startAgencyName,callback:function(a){t.$set(t.taskInfo,"startAgencyName",a)},expression:"taskInfo.startAgencyName"}})],1),t._v(" "),e("div",{staticClass:"form-item"},[e("span",{staticClass:"span-title"},[t._v("目的地机构：")]),t._v(" "),e("el-input",{model:{value:t.taskInfo.endAgencyName,callback:function(a){t.$set(t.taskInfo,"endAgencyName",a)},expression:"taskInfo.endAgencyName"}})],1),t._v(" "),e("div",{staticClass:"form-item"},[e("span",{staticClass:"span-title"},[t._v("出车时间：")]),t._v(" "),e("el-input",{model:{value:t.taskInfo.outStorageTime,callback:function(a){t.$set(t.taskInfo,"outStorageTime",a)},expression:"taskInfo.outStorageTime"}})],1),t._v(" "),e("div",{staticClass:"form-item"},[e("span",{staticClass:"span-title"},[t._v("回车时间：")]),t._v(" "),e("el-input",{model:{value:t.taskInfo.intoStorageTime,callback:function(a){t.$set(t.taskInfo,"intoStorageTime",a)},expression:"taskInfo.intoStorageTime"}})],1),t._v(" "),e("div",{staticClass:"form-item driver"},[e("span",{staticClass:"span-title"},[t._v("司机：")]),t._v(" "),e("el-input",{model:{value:t.taskInfo.driverName,callback:function(a){t.$set(t.taskInfo,"driverName",a)},expression:"taskInfo.driverName"}})],1)])])],1)])],1),t._v(" "),"是"===t.taskInfo.isFault||"是"===t.taskInfo.isBreakRules||"是"===t.taskInfo.isAccident?e("div",{staticClass:"by-accident"},[e("el-container",{staticClass:"customer-details-box breakdown-info",staticStyle:{"margin-top":"20px"}},[e("el-card",{staticStyle:{width:"100%"},attrs:{shadow:"never"}},[e("div",{staticClass:"block"},[e("div",{staticClass:"car-base task-info"},[t._v("故障信息")]),t._v(" "),e("el-card",{staticClass:"form-box",attrs:{shadow:"never"}},[e("div",{staticClass:"form-item-box"},[e("div",{staticClass:"form-item"},[e("span",{staticClass:"span-title"},[t._v("车辆故障：")]),t._v(" "),e("el-input",{model:{value:t.taskInfo.isFault,callback:function(a){t.$set(t.taskInfo,"isFault",a)},expression:"taskInfo.isFault"}})],1),t._v(" "),e("div",{staticClass:"form-item"},[e("span",{staticClass:"span-title"},[t._v("是否可用：")]),t._v(" "),e("el-input",{model:{value:t.taskInfo.isAvailable,callback:function(a){t.$set(t.taskInfo,"isAvailable",a)},expression:"taskInfo.isAvailable"}})],1),t._v(" "),e("div",{staticClass:"form-item"},[e("span",{staticClass:"span-title"},[t._v("故障类型：")]),t._v(" "),e("el-input",{model:{value:t.taskInfo.faultType,callback:function(a){t.$set(t.taskInfo,"faultType",a)},expression:"taskInfo.faultType"}})],1),t._v(" "),e("div",{staticClass:"form-item",staticStyle:{"align-items":"flex-start"}},[e("span",{staticClass:"span-title"},[t._v("故障图片：")]),t._v(" "),t.taskInfo.faultImages.length?e("div",{staticStyle:{display:"flex"}},t._l(t.taskInfo.faultImages,(function(a,i){return e("div",{key:i,staticClass:"img-box"},[e("img",{staticStyle:{width:"212px",height:"159px","border-radius":"4px"},attrs:{src:a}}),t._v(" "),e("div",{staticClass:"img-shadow"},[e("img",{staticClass:"el-upload-span searchBigImg",attrs:{src:s("3fdf")},on:{click:function(s){return s.stopPropagation(),t.searchBigImg(a)}}})])])})),0):e("div",{staticStyle:{color:"#818693","font-size":"14px"}},[t._v("无")])])])])],1)])],1),t._v(" "),e("el-container",{staticClass:"customer-details-box breakRules-info",staticStyle:{"margin-top":"20px"}},[e("el-card",{staticStyle:{width:"100%"},attrs:{shadow:"never"}},[e("div",{staticClass:"block"},[e("div",{staticClass:"car-base task-info"},[t._v("违章信息")]),t._v(" "),e("el-card",{staticClass:"form-box",attrs:{shadow:"never"}},[e("div",{staticClass:"form-item-box"},[e("div",{staticClass:"form-item"},[e("span",{staticClass:"span-title"},[t._v("车辆违章：")]),t._v(" "),e("el-input",{model:{value:t.taskInfo.isBreakRules,callback:function(a){t.$set(t.taskInfo,"isBreakRules",a)},expression:"taskInfo.isBreakRules"}})],1),t._v(" "),e("div",{staticClass:"form-item"},[e("span",{staticClass:"span-title"},[t._v("违章类型：")]),t._v(" "),e("el-input",{model:{value:t.taskInfo.breakRulesType,callback:function(a){t.$set(t.taskInfo,"breakRulesType",a)},expression:"taskInfo.breakRulesType"}})],1),t._v(" "),e("div",{staticClass:"form-item"},[e("span",{staticClass:"span-title"},[t._v("罚款金额：")]),t._v(" "),e("el-input",{model:{value:t.taskInfo.penaltyAmount,callback:function(a){t.$set(t.taskInfo,"penaltyAmount",a)},expression:"taskInfo.penaltyAmount"}})],1),t._v(" "),e("div",{staticClass:"form-item",staticStyle:{"align-items":"flex-start"}},[e("span",{staticClass:"span-title"},[t._v("扣分：")]),t._v(" "),e("el-input",{model:{value:t.taskInfo.deductPoints,callback:function(a){t.$set(t.taskInfo,"deductPoints",a)},expression:"taskInfo.deductPoints"}})],1)])])],1)])],1),t._v(" "),e("el-container",{staticClass:"customer-details-box accident-info",staticStyle:{"margin-top":"20px","margin-bottom":"60px"}},[e("el-card",{staticStyle:{width:"100%"},attrs:{shadow:"never"}},[e("div",{staticClass:"block"},[e("div",{staticClass:"car-base task-info"},[t._v("事故信息")]),t._v(" "),e("el-card",{staticClass:"form-box",attrs:{shadow:"never"}},[e("div",{staticClass:"form-item-box"},[e("div",{staticClass:"form-item"},[e("span",{staticClass:"span-title"},[t._v("车辆事故：")]),t._v(" "),e("el-input",{model:{value:t.taskInfo.isAccident,callback:function(a){t.$set(t.taskInfo,"isAccident",a)},expression:"taskInfo.isAccident"}})],1),t._v(" "),e("div",{staticClass:"form-item"},[e("span",{staticClass:"span-title"},[t._v("事故类型：")]),t._v(" "),e("el-input",{model:{value:t.taskInfo.accidentType,callback:function(a){t.$set(t.taskInfo,"accidentType",a)},expression:"taskInfo.accidentType"}})],1),t._v(" "),e("div",{staticClass:"form-item",staticStyle:{"align-items":"flex-start","margin-top":"20px!important"}},[e("span",{staticClass:"span-title"},[t._v("事故图片：")]),t._v(" "),t.taskInfo.accidentImages.length?e("div",{staticStyle:{display:"flex"}},t._l(t.taskInfo.accidentImages,(function(a,i){return e("div",{key:i,staticClass:"img-box"},[e("img",{staticStyle:{width:"212px",height:"159px","border-radius":"4px"},attrs:{src:a}}),t._v(" "),e("div",{staticClass:"img-shadow"},[e("img",{staticClass:"el-upload-span searchBigImg",attrs:{src:s("3fdf")},on:{click:function(s){return s.stopPropagation(),t.searchBigImg(a)}}})])])})),0):e("div",{staticStyle:{color:"#818693","font-size":"14px"}},[t._v("无")])])])])],1)])],1)],1):e("el-container",{staticClass:"customer-details-box breakdown-info",staticStyle:{"margin-top":"20px"}},[e("el-card",{staticStyle:{width:"100%"},attrs:{shadow:"never"}},[e("div",{staticClass:"block"},[e("div",{staticClass:"car-base task-info"},[t._v("异常信息")]),t._v(" "),e("el-card",{staticClass:"form-box",attrs:{shadow:"never"}},[e("div",{staticClass:"form-item-box"},[e("div",{staticClass:"form-item"},[e("span",{staticClass:"span-title"},[t._v("车辆故障：")]),t._v(" "),e("el-input",{model:{value:t.taskInfo.isFault,callback:function(a){t.$set(t.taskInfo,"isFault",a)},expression:"taskInfo.isFault"}})],1),t._v(" "),e("div",{staticClass:"form-item"},[e("span",{staticClass:"span-title"},[t._v("车辆违章：")]),t._v(" "),e("el-input",{model:{value:t.taskInfo.isBreakRules,callback:function(a){t.$set(t.taskInfo,"isBreakRules",a)},expression:"taskInfo.isBreakRules"}})],1),t._v(" "),e("div",{staticClass:"form-item"},[e("span",{staticClass:"span-title"},[t._v("车辆事故：")]),t._v(" "),e("el-input",{model:{value:t.taskInfo.isAccident,callback:function(a){t.$set(t.taskInfo,"isAccident",a)},expression:"taskInfo.isAccident"}})],1)])])],1)])],1)],1)]),t._v(" "),e("el-dialog",{attrs:{visible:t.dialogVisible,width:"600px",height:"600px"},on:{"update:visible":function(a){t.dialogVisible=a}}},[e("img",{attrs:{width:"100%",src:t.imageUrl,alt:""}})])],1)},i=[],n=(s("28a5"),s("96cf"),s("3b8d")),l=s("95e9"),c={data:function(){return{imageUrl:"",dialogVisible:!1,taskInfo:{transportTaskId:"",licensePlate:"",startAgencyName:"",endAgencyName:"",outStorageTime:"",intoStorageTime:"",driverName:"",isFault:"",isAvailable:"",faultType:"",faultImages:[],isBreakRules:"",breakRulesType:"",penaltyAmount:"",deductPoints:"",accidentImages:[],isAccident:"",accidentType:""},breakRulesType:[{key:1,value:"闯红灯"},{key:2,value:"无证驾驶"},{key:3,value:"超载"},{key:4,value:"酒后驾驶"},{key:5,value:"超速行驶,可用"}],accidentType:[{key:1,value:"直行事故"},{key:2,value:"追尾事故"},{key:3,value:"超车事故"},{key:4,value:"左转弯事故"},{key:5,value:"右转弯事故"},{key:6,value:"弯道事故"},{key:7,value:"坡道事故"},{key:8,value:"会车事故"},{key:9,value:"其他,可用"}],faultType:[{key:1,value:"发动机启动困难"},{key:2,value:"不着车"},{key:3,value:"漏油"},{key:4,value:"漏水"},{key:5,value:"照明失灵"},{key:6,value:"有异响"},{key:7,value:"排烟异常"},{key:8,value:"温度异常"},{key:9,value:"其他,可用"}]}},created:function(){this.id=this.$route.query.id,this.getList(this.id)},methods:{searchBigImg:function(t){this.imageUrl=t,this.dialogVisible=!0},getList:function(){var t=Object(n["a"])(regeneratorRuntime.mark((function t(a){var s,e;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(l["a"])(a);case 2:s=t.sent,e=s.data,this.taskInfo=e,this.taskInfo.isFault=e.isFault?"是":"否",this.taskInfo.isAvailable=e.isAvailable?"是":"否",this.taskInfo.faultType=e.faultType?this.faultType.filter((function(t){return t.key===e.faultType}))[0].value:"无",this.taskInfo.breakRulesType=e.breakRulesType?this.breakRulesType.filter((function(t){return t.key===e.breakRulesType}))[0].value:"无",this.taskInfo.isBreakRules=e.isBreakRules?"是":"否",this.taskInfo.penaltyAmount=e.penaltyAmount+"元",this.taskInfo.deductPoints=e.deductPoints+"分",this.taskInfo.isAccident=e.isAccident?"是":"否",this.taskInfo.accidentType=e.accidentType?this.accidentType.filter((function(t){return t.key===e.accidentType}))[0].value:"无",this.taskInfo.accidentImages=e.accidentImages?e.accidentImages.split(","):[],this.taskInfo.faultImages=e.faultImages?e.faultImages.split(","):[];case 16:case"end":return t.stop()}}),t,this)})));function a(a){return t.apply(this,arguments)}return a}()}},o=c,r=(s("8705"),s("9a62"),s("2877")),u=Object(r["a"])(o,e,i,!1,null,"19e459cd",null);a["default"]=u.exports},"3fdf":function(t,a){t.exports="data:image/png;base64,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"},"40c0":function(t,a,s){},8705:function(t,a,s){"use strict";var e=s("40c0"),i=s.n(e);i.a},"95e9":function(t,a,s){"use strict";s.d(a,"l",(function(){return i})),s.d(a,"i",(function(){return n})),s.d(a,"j",(function(){return l})),s.d(a,"k",(function(){return c})),s.d(a,"m",(function(){return o})),s.d(a,"e",(function(){return r})),s.d(a,"d",(function(){return u})),s.d(a,"f",(function(){return f})),s.d(a,"c",(function(){return d})),s.d(a,"n",(function(){return v})),s.d(a,"g",(function(){return p})),s.d(a,"b",(function(){return k})),s.d(a,"a",(function(){return m})),s.d(a,"h",(function(){return b}));var e=s("b775"),i=function(t){return Object(e["a"])("/goodsType/page","get",t)},n=function(t){return Object(e["a"])("/goodsType","post",t)},l=function(t){return Object(e["a"])("/goodsType/".concat(t),"delete",t)},c=function(t){return Object(e["a"])("/goodsType/".concat(t),"get",t)},o=function(t,a){return Object(e["a"])("/goodsType/".concat(t),"put",a)},r=function(t){return Object(e["a"])("/business-hall/courier/page","get",t)},u=function(t){return Object(e["a"])("/business-hall/courier/".concat(t),"get",t)},f=function(t,a){return Object(e["a"])("/business-hall/scope/".concat(t,"/").concat(a),"get",t)},d=function(t){return Object(e["a"])("/business-hall/scope","post",t)},v=function(t){return Object(e["a"])("/pickup-dispatch-task-manager/page","post",t)},p=function(t,a){return Object(e["a"])("/business-hall/scope/".concat(t,"/").concat(a),"delete")},k=function(t){return Object(e["a"])("/truck-return-register/pageQuery","post",t)},m=function(t){return Object(e["a"])("/truck-return-register/detail/".concat(t),"get",t)},b=function(t){return Object(e["a"])("/pickup-dispatch-task-manager/".concat(t.courierId),"put",t.ids)}},"9a62":function(t,a,s){"use strict";var e=s("e5b6"),i=s.n(e);i.a},e5b6:function(t,a,s){}}]);