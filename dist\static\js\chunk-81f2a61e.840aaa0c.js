(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-81f2a61e"],{"0623":function(n,t,c){},"940e":function(n,t,c){},"9c94":function(n,t,c){"use strict";var e=c("940e"),a=c.n(e);a.a},b299:function(n,t,c){"use strict";c.r(t);var e=function(){var n=this,t=n.$createElement,c=n._self._c||t;return c("div",{staticClass:"dashboard-container order-list customer-list-box"},[n._v("\n  订单管理\n")])},a=[],s={},i=s,r=(c("b96b"),c("9c94"),c("2877")),o=Object(r["a"])(i,e,a,!1,null,"3d77a83a",null);t["default"]=o.exports},b96b:function(n,t,c){"use strict";var e=c("0623"),a=c.n(e);a.a}}]);