(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2c5692f5"],{"5c0d":function(t,e,n){"use strict";var i=n("e0dc"),a=n.n(i);a.a},"95e9":function(t,e,n){"use strict";n.d(e,"l",(function(){return a})),n.d(e,"i",(function(){return s})),n.d(e,"j",(function(){return r})),n.d(e,"k",(function(){return o})),n.d(e,"m",(function(){return c})),n.d(e,"e",(function(){return l})),n.d(e,"d",(function(){return u})),n.d(e,"f",(function(){return h})),n.d(e,"c",(function(){return p})),n.d(e,"n",(function(){return d})),n.d(e,"g",(function(){return g})),n.d(e,"b",(function(){return f})),n.d(e,"a",(function(){return y})),n.d(e,"h",(function(){return v}));var i=n("b775"),a=function(t){return Object(i["a"])("/goodsType/page","get",t)},s=function(t){return Object(i["a"])("/goodsType","post",t)},r=function(t){return Object(i["a"])("/goodsType/".concat(t),"delete",t)},o=function(t){return Object(i["a"])("/goodsType/".concat(t),"get",t)},c=function(t,e){return Object(i["a"])("/goodsType/".concat(t),"put",e)},l=function(t){return Object(i["a"])("/business-hall/courier/page","get",t)},u=function(t){return Object(i["a"])("/business-hall/courier/".concat(t),"get",t)},h=function(t,e){return Object(i["a"])("/business-hall/scope/".concat(t,"/").concat(e),"get",t)},p=function(t){return Object(i["a"])("/business-hall/scope","post",t)},d=function(t){return Object(i["a"])("/pickup-dispatch-task-manager/page","post",t)},g=function(t,e){return Object(i["a"])("/business-hall/scope/".concat(t,"/").concat(e),"delete")},f=function(t){return Object(i["a"])("/truck-return-register/pageQuery","post",t)},y=function(t){return Object(i["a"])("/truck-return-register/detail/".concat(t),"get",t)},v=function(t){return Object(i["a"])("/pickup-dispatch-task-manager/".concat(t.courierId),"put",t.ids)}},a7a2:function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"map-content"},[i("div",{staticClass:"app-container"},[i("el-card",{staticClass:"card-box",attrs:{shadow:"never"}},[i("div",{staticClass:"header-box"},[i("span",[t._v("作业范围分配")]),t._v(" "),i("div",{staticStyle:{float:"right"}},[i("el-button",{directives:[{name:"show",rawName:"v-show",value:t.showEdit,expression:"showEdit"}],staticClass:"save-map-btn",staticStyle:{"background-color":"#e15536",color:"#ffffff",width:"90px",border:"1px solid #e15536"},on:{click:t.editData}},[t._v("编辑")]),t._v(" "),i("el-button",{directives:[{name:"show",rawName:"v-show",value:t.showSave,expression:"showSave"}],staticClass:"save-map-btn",staticStyle:{"background-color":"#e15536",color:"#ffffff",width:"90px",border:"1px solid #e15536"},on:{click:t.createData}},[t._v("保存")]),t._v(" "),i("el-button",{directives:[{name:"show",rawName:"v-show",value:t.showSave,expression:"showSave"}],staticClass:"cancel-map-btn",staticStyle:{width:"90px"},on:{click:t.cancelData}},[t._v("取消")])],1)]),t._v(" "),i("div",{staticClass:"searchBox"},[i("div",{staticClass:"keyword"},[i("label",[t._v("地区关键词：")]),t._v(" "),i("el-input",{attrs:{placeholder:"请输入地区关键词"},on:{input:t.handleInputfun},model:{value:t.keyword,callback:function(e){t.keyword=e},expression:"keyword"}})],1)]),t._v(" "),i("baidu-map",{ref:"myMap",staticClass:"map",class:t.keyword?"":"active",attrs:{center:t.center,zoom:t.zoom,"scroll-wheel-zoom":!0,"map-click":!1},on:{mousemove:t.syncPolyline,click:t.paintPolyline,rightclick:t.newPolyline,ready:t.ready}},[i("bm-local-search",{attrs:{keyword:t.keyword,"auto-viewport":!0,panel:!1}}),t._v(" "),i("bm-control",{directives:[{name:"show",rawName:"v-show",value:t.showSave,expression:"showSave"}],staticStyle:{background:"#fff",width:"100%"}},[i("el-button",{staticStyle:{background:"rgba(225, 85, 54, 1)",border:"1px solid rgba(225, 85, 54, 1)"},attrs:{type:"primary"},on:{click:function(e){return t.toggle("polyline")}}},[t._v(t._s(t.polyline.editing?"完成绘制":"开始绘制"))]),t._v(" "),i("el-button",{directives:[{name:"show",rawName:"v-show",value:!1!==t.polyline.editing,expression:"polyline.editing !== false"}],staticClass:"dele",staticStyle:{color:"#818693"},on:{click:function(e){return t.clear()}}},[t._v("删除围栏")]),t._v(" "),i("P",{staticStyle:{"font-size":"12px",color:"rgba(198, 126, 18, 1)",width:"100%",background:"rgba(255, 245, 231, 1)",height:"30px","line-height":"30px","text-align":"left","padding-left":"10px"}},[i("img",{staticStyle:{width:"14px","vertical-align":"middle","margin-bottom":"2px","margin-right":"6px"},attrs:{src:n("57bd"),alt:""}}),t._v("\n            点击或拖动鼠标绘制电子围栏，右击鼠标结束图形绘制，图形不可有交叉点。完成绘制后，点击提交按钮保存。仅支持绘制单个电子围栏区域\n          ")])],1),t._v(" "),i("bm-control",{directives:[{name:"show",rawName:"v-show",value:!t.isHaveAgencyArange&&!t.showSave,expression:"!isHaveAgencyArange &&!showSave"}],staticStyle:{background:"#fff",width:"100%"}},[i("p",{staticStyle:{"font-size":"12px",color:"rgba(198, 126, 18, 1)",width:"100%",background:"rgba(255, 245, 231, 1)",height:"30px","line-height":"30px","text-align":"center"}},[i("img",{staticStyle:{width:"14px","vertical-align":"middle","margin-bottom":"2px","margin-right":"6px"},attrs:{src:n("57bd"),alt:""}}),t._v("\n            当前"+t._s(t.$route.query.agencyName)+"下暂无作业范围\n          ")])]),t._v(" "),t._l(t.polyline.paths,(function(e,n){return i("bm-polygon",{key:n,attrs:{path:e,"stroke-color":"#e85552","fill-color":"rgba(232,85,82,0.30)","fill-opacity":.9,"stroke-opacity":.5,"stroke-weight":2,editing:t.polyline.editing,"stroke-style":"dashed"},on:{lineupdate:t.updatePolygonPath}})}))],2)],1)],1)])},a=[],s=(n("7f7f"),n("ac6a"),n("75fc")),r=(n("96cf"),n("3b8d")),o=n("95e9"),c={data:function(){return{isHaveAgencyArange:!1,keyword:"",showEdit:!0,showSave:!1,center:{lng:"116.404",lat:"39.915"},path:"",zoom:13,markList:"",agencyType:"",agencyTypeName:"",agencyId:"",agencyName:"",parentAgencyId:"",parentAgencyName:"",polygonPath:[],isOperation:!1,polyline:{editing:!1,paths:[]},submitPath:[],countrys:[],disDeltePaths:[],newOptionAreas:null,isFirstEdit:!0}},mounted:function(){this.paintPolyline()},methods:{handleInput:function(t){this.keyword=t.target.value},ready:function(t){var e=t.BMap,n=t.map;this.map=n,this.BMap=e,this.handleNodeClick(this.$route.query.id)},goBack:function(){history.go(-1)},handleNodeClick:function(){var t=Object(r["a"])(regeneratorRuntime.mark((function t(e){var n,i,a,r,c;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return this.polyline.paths=[],t.next=3,Object(o["f"])(e,2);case 3:n=t.sent,i=n.data,a=[],r=[],i.polygon&&i.polygon.length>0?(a=i.polygon.map((function(t){return{lat:t.latitude,lng:t.longitude}})),this.isHaveAgencyArange=!0):(this.isHaveAgencyArange=!1,r=i.latitude?[{lat:i.latitude,lng:i.longitude}]:[{lat:"39.915",lng:"116.4045"}]),c=this.map&&this.map.getViewport(this.isHaveAgencyArange?a:r),this.$set(this.center,"lng",c.center.lng),this.$set(this.center,"lat",c.center.lat),this.zoom=this.isHaveAgencyArange?c.zoom:11,this.$set(this.polyline,"paths",a?[a]:[]),this.isFirstEdit=Boolean(i.polygon&&i.polygon.length>0),this.newOptionAreas=[],this.disDeltePaths=Object(s["a"])(this.polygonPath);case 16:case"end":return t.stop()}}),t,this)})));function e(e){return t.apply(this,arguments)}return e}(),createData:function(){var t=Object(r["a"])(regeneratorRuntime.mark((function t(){var e,n,i,a,s,r=this;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.isOperation=!1,0!==this.polyline.paths.length){t.next=3;break}return t.abrupt("return",this.$message.error("请先绘制快递员作业范围"));case 3:return e=[],this.polyline.paths.forEach((function(t){e.push(t.length)})),n=Math.max.apply(Math,e),i=e.indexOf(n),a=[],a=(this.isFirstEdit?this.submitPath:this.polyline.paths[i]).map((function(t){return{latitude:t.lat,longitude:t.lng}})),a.push(a[0]),s={polygon:a,type:2,bid:this.$route.query.id},t.next=13,Object(o["c"])(s).then((function(t){200===t.code?(r.showSave=!1,r.showEdit=!0,r.$message.success("电子围栏保存成功"),r.polyline.paths=[],r.$set(r.polyline,"editing",!1),r.handleNodeClick(r.$route.query.id)):r.$message.error(t.msg||"电子围栏保存失败")})).catch((function(){r.$message.error("电子围栏保存失败")}));case 13:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),handleInputfun:function(){this.$set(this.center,"lng",0),this.$set(this.center,"lat",0),this.zoom=0},editData:function(){var t=Object(r["a"])(regeneratorRuntime.mark((function t(){var e;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:this.showSave=!0,this.showEdit=!1,e=this.map&&this.map.getViewport(this.polyline.paths[0]),this.$set(this.center,"lng",e.center.lng),this.$set(this.center,"lat",e.center.lat),this.zoom=this.isHaveAgencyArange?e.zoom:11;case 6:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),cancelData:function(){this.showSave=!1,this.showEdit=!0,this.polyline.editing=!1},toggle:function(t){this[t].editing=!this[t].editing},clear:function(){var t=Object(r["a"])(regeneratorRuntime.mark((function t(){var e;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.polyline.editing=!1,this.showSave=!0,this.showEdit=!1,this.isFirstEdit){t.next=7;break}this.$set(this.polyline,"paths",[]),t.next=11;break;case 7:return t.next=9,Object(o["g"])(this.$route.query.id,2);case 9:e=t.sent,200===e.code?(this.$message.success("删除快递员作业范围成功"),this.polyline.paths=[],this.handleNodeClick(this.$route.query.id)):this.$message.error(e.msg||"删除机构作业范围成功");case 11:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),updatePolygonPath:function(t){this.submitPath=t.target.getPath()},syncPolyline:function(t){if(this.polyline.editing&&this.isOperation){var e=this.polyline.paths;if(e.length){var n=e[e.length-1];n.length&&(1===n.length&&n.push(t.point),this.$set(n,n.length-1,t.point))}}},newPolyline:function(t){if(this.polyline.editing){var e=this.polyline.paths;e.length||e.push([]);var n=e[e.length-1];n.pop(),n.length&&e.push([])}},paintPolyline:function(t){if(this.polyline.editing&&!this.isFirstEdit){this.isOperation=!0;var e=this.polyline.paths;!e.length&&e.push([]),e[e.length-1].push(t.point)}}}},l=c,u=(n("5c0d"),n("2877")),h=Object(u["a"])(l,i,a,!1,null,"748cd237",null);e["default"]=h.exports},e0dc:function(t,e,n){}}]);