/* eslint-disable indent */
// import router from './router'
// import store from './store'
// import { Message } from 'element-ui'
// import NProgress from 'nprogress' // progress bar
// import 'nprogress/nprogress.css' // progress bar style
// import { getToken } from '@/utils/auth' // get token from cookie
// import getPageTitle from '@/utils/get-page-title'
// eslint-disable-next-line space-before-function-paren

// NProgress.configure({ showSpinner: false }) // NProgress Configuration

// const whiteList = ['/login'] // no redirect whitelist
// eslint-disable-next-line space-before-function-paren
// router.beforeEach(async (to, from, next) => {
//     next()
// })

// router.afterEach(() => {
//     // finish progress bar
//     NProgress.done()
// })
