(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-382a4c00"],{"1687f":function(t,e,a){},"2b6d":function(t,e,a){"use strict";var n=a("1687f"),r=a.n(n);r.a},"3e92":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"dashboard-container dispatch-task customer-list-box",staticStyle:{width:"100%"}},[n("div",{staticClass:"app-container"},[n("el-card",{staticClass:"search-card-box",attrs:{shadow:"never"}},[n("el-form",{ref:"operationalSearchFormData",attrs:{model:t.operationalSearchFormData,"label-width":"80px"}},[n("div",{staticClass:"filter-container"},[n("el-row",{attrs:{gutter:60}},[n("el-col",{attrs:{span:8}},[n("el-form-item",{staticStyle:{"margin-bottom":"0px"},attrs:{label:"运输任务编号:","label-width":"110px"}},[n("el-input",{attrs:{placeholder:"请输入运输任务编号",clearable:""},model:{value:t.operationalSearchFormData.transportTaskId,callback:function(e){t.$set(t.operationalSearchFormData,"transportTaskId",e)},expression:"operationalSearchFormData.transportTaskId"}})],1)],1),t._v(" "),n("el-col",{attrs:{span:8}},[n("el-form-item",{staticStyle:{"margin-bottom":"20px"},attrs:{label:"起始地机构:","label-width":"110px"}},[n("treeselect",{staticStyle:{width:"100%"},attrs:{options:t.agencyOptions,normalizer:t.normalizer,searchable:!0,placeholder:"请选择起始地机构","no-options-text":"暂无数据","no-results-text":"暂无数据"},on:{select:t.selectStartAgency,open:t.open,close:t.close},model:{value:t.operationalSearchFormData.startAgencyId,callback:function(e){t.$set(t.operationalSearchFormData,"startAgencyId",e)},expression:"operationalSearchFormData.startAgencyId"}}),t._v(" "),n("img",{ref:"arrow",staticStyle:{position:"absolute",width:"18px",height:"18px",right:"9px",top:"12px",cursor:"pointer","pointer-events":"none","transition-duration":"0.3s"},attrs:{src:a("5ae1")}})],1)],1),t._v(" "),n("el-col",{attrs:{span:8}},[n("el-form-item",{staticStyle:{"margin-bottom":"20px"},attrs:{label:"目的地机构:","label-width":"110px"}},[n("treeselect",{staticStyle:{width:"100%"},attrs:{options:t.agencyOptions,normalizer:t.normalizer,searchable:!0,placeholder:"请选择目的地机构","no-options-text":"暂无数据","no-results-text":"暂无数据"},on:{select:t.selectEndAgency,open:t.opens,close:t.closes},model:{value:t.operationalSearchFormData.endAgencyId,callback:function(e){t.$set(t.operationalSearchFormData,"endAgencyId",e)},expression:"operationalSearchFormData.endAgencyId"}}),t._v(" "),n("img",{ref:"arrows",staticStyle:{position:"absolute",width:"18px",height:"18px",right:"9px",top:"12px",cursor:"pointer","pointer-events":"none","transition-duration":"0.3s"},attrs:{src:a("5ae1")}})],1)],1),t._v(" "),n("el-col",{attrs:{span:8}},[n("el-form-item",{staticStyle:{"margin-bottom":"20px"},attrs:{label:"回车时间:","label-width":"110px"}},[n("el-date-picker",{attrs:{format:"yyyy-MM-dd HH:mm:ss","value-format":"yyyy-MM-dd HH:mm:ss",type:"datetimerange",placeholder:"选择回车时间","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间"},on:{change:t.handleEstimatedTimeChange},model:{value:t.intoStorage,callback:function(e){t.intoStorage=e},expression:"intoStorage"}})],1)],1),t._v(" "),n("el-col",{attrs:{span:8}},[n("el-form-item",{staticStyle:{"margin-bottom":"20px"},attrs:{label:"车辆是否可用:","label-width":"110px"}},[n("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:t.operationalSearchFormData.isAvailable,callback:function(e){t.$set(t.operationalSearchFormData,"isAvailable",e)},expression:"operationalSearchFormData.isAvailable"}},t._l(t.carStatus,(function(t){return n("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1)],1),t._v(" "),n("el-col",{attrs:{span:8}},[n("el-button",{attrs:{type:"warning"},on:{click:function(e){return t.handleFilter("查询")}}},[t._v("搜索")]),t._v(" "),n("el-button",{staticClass:"reset-btn",attrs:{plain:""},on:{click:function(e){return t.resetForm("operationalSearchFormData")}}},[t._v("重置")])],1)],1)],1)])],1),t._v(" "),n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],class:{"loading-box":t.listLoading},staticStyle:{"margin-top":"20px"},attrs:{"element-loading-text":"加载中"}},[n("el-card",{staticClass:"table-card-box",attrs:{shadow:"never"}},[n("el-table",{key:t.tableKey,ref:"multipleTable",staticStyle:{width:"100%"},attrs:{data:t.dataList,fit:"",stripe:"","header-cell-style":{background:"rgba(250,252,255,1)"}},on:{"selection-change":t.handleSelectionChange}},[n("div",{directives:[{name:"show",rawName:"v-show",value:(!t.dataList||t.dataList.length<=0)&&!t.listLoading&&!t.searchkey,expression:"\n              (!dataList || dataList.length <= 0) &&\n                !listLoading &&\n                !searchkey\n            "}],attrs:{slot:"empty"},slot:"empty"},[n("img",{staticStyle:{"margin-top":"20px",width:"25%",height:"25%"},attrs:{src:a("aefe"),alt:"img"}}),t._v(" "),n("p",{staticStyle:{"margin-top":"-20px","padding-bottom":"0px"}},[t._v("这里空空如也")])]),t._v(" "),n("el-card",{directives:[{name:"show",rawName:"v-show",value:(!t.dataList||t.dataList.length<=0)&&!t.listLoading&&t.searchkey,expression:"\n              (!dataList || dataList.length <= 0) && !listLoading && searchkey\n            "}],staticClass:"table-empty-box",attrs:{slot:"empty",shadow:"never"},slot:"empty"},[n("empty")],1),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"运输任务编号","min-width":"180px",prop:"transportTaskId"}}),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"起始地机构","min-width":"180",prop:"startAgencyName"}}),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"目的地机构","min-width":"180",prop:"endAgencyName"}}),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"运单数量","min-width":"120",prop:"transportOrderNumber"}}),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"出车时间","min-width":"160",prop:"outStorageTime"}}),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"回车时间","min-width":"160",prop:"intoStorageTime"}}),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"车牌号码","min-width":"160",prop:"licensePlate"}}),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"车辆是否可用","min-width":"120px"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("div",{staticClass:"tableColumn-status",class:{"stop-use":!e.row.isAvailable}},[t._v("\n                "+t._s(String(e.row.isAvailable)?"可用":"不可用")+"\n              ")])]}}])}),t._v(" "),n("el-table-column",{attrs:{align:"center",label:"操作",fixed:"right","class-name":"small-padding fixed-width"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(a){return t.handleWayllDetail(e.row.id)}}},[t._v("查看")])]}}])})],1),t._v(" "),t.total>10?n("div",{staticClass:"pagination"},[n("div",{staticClass:"pages"},[n("el-pagination",{attrs:{"current-page":Number(t.operationalSearchFormData.page),total:Number(t.total),"page-size":Number(t.operationalSearchFormData.pageSize),"page-sizes":[10,20,30,50],layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)]):t._e()],1)],1)],1)])},r=[],i=(a("96cf"),a("3b8d")),o=(a("7f7f"),a("95e9")),s=(a("542c"),a("ca17")),l=a.n(s),c=a("5850"),u=a("ed08"),d={components:{Treeselect:l.a},data:function(){return{tableKey:0,dataList:[],total:null,searchkey:!1,tableChecked:[],carStatus:[{id:3,name:"全部"},{id:1,name:"可用"},{id:2,name:"不可用"}],listLoading:!0,operationalSearchFormData:{page:1,pageSize:10,transportTaskId:"",startAgencyId:null,endAgencyId:null,isAvailable:"",intoStorageStartTime:"",intoStorageEndTime:""},intoStorage:[],agencyOptions:[],normalizer:function(t){return{id:t.id,label:t.name,children:t.children}}}},mounted:function(){},created:function(){this.getAgencyList(),this.initialDate(),document.onkeydown=function(t){var e=window.event.keyCode;13===e&&this.handleFilter(this.operationalSearchFormData)}},updated:function(){},methods:{getAgencyList:function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(){var e,a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(c["a"])();case 2:e=t.sent,a=e.data,this.agencyOptions=JSON.parse(a);case 5:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),selectStartAgency:function(t){this.operationalSearchFormData.startAgencyId=t.id},selectEndAgency:function(t){this.operationalSearchFormData.endAgencyId=t.id},handleEstimatedTimeChange:function(t){t?(this.intoStorage=t,this.operationalSearchFormData.intoStorageStartTime=t[0],this.operationalSearchFormData.intoStorageEndTime=t[1]):(this.intoStorage=[],this.operationalSearchFormData.intoStorageStartTime="",this.operationalSearchFormData.intoStorageEndTime="")},opens:function(){this.$refs.arrows.style.transform="rotate(-180deg)"},closes:function(){this.$refs.arrows.style.transform="rotate(0deg)"},open:function(){this.$refs.arrow.style.transform="rotate(-180deg)"},close:function(){this.$refs.arrow.style.transform="rotate(0deg)"},cancelDisFn:function(){this.$refs["dataForm"].resetFields(),this.dialogFormVisible=!1},initialDate:function(){this.getList()},handleSelectionChange:function(t){this.tableChecked=t},getList:function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(){var e,a,n;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return this.listLoading=!0,e=Object(u["b"])(this.operationalSearchFormData),3===e.isAvailable?delete e.isAvailable:2===e.isAvailable?e.isAvailable=!1:1===e.isAvailable&&(e.isAvailable=!0),t.next=5,Object(o["b"])(e);case 5:a=t.sent,n=a.data,this.listLoading=!1,this.dataList=n.items,this.total=n.counts;case 10:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),resetForm:function(){this.$refs["operationalSearchFormData"].resetFields(),this.operationalSearchFormData.page=1,this.operationalSearchFormData.pageSize=10,this.operationalSearchFormData.transportTaskId="",this.operationalSearchFormData.startAgencyId="",this.operationalSearchFormData.endAgencyId="",this.searchkey=!1,this.operationalSearchFormData.isAvailable="",this.operationalSearchFormData.intoStorageStartTime="",this.operationalSearchFormData.intoStorageEndTime="",this.intoStorage=[],this.getList()},handleFilter:function(){this.getList(),this.searchkey=!0},handleSizeChange:function(t){this.operationalSearchFormData.pageSize=t,1===this.operationalSearchFormData.page&&this.getList()},handleCurrentChange:function(t){this.operationalSearchFormData.page=t,this.getList()},handleWayllDetail:function(t){this.$router.push({path:"/transit/car-register-info",query:{id:t}})}}},p=d,h=(a("2b6d"),a("baa0"),a("2877")),g=Object(h["a"])(p,n,r,!1,null,"4b4ff9ca",null);e["default"]=g.exports},5850:function(t,e,a){"use strict";a.d(e,"a",(function(){return r})),a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return o})),a.d(e,"d",(function(){return s}));var n=a("b775"),r=function(t){return Object(n["a"])("/business-hall/tree","get",t)},i=function(t){return Object(n["a"])("/business-hall/".concat(t),"get",t)},o=function(t){return Object(n["a"])("/business-hall/user/page","get",t)},s=function(t){return Object(n["a"])("/business-hall","post",t)}},"5ae1":function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAcCAYAAAB2+A+pAAAAAXNSR0IArs4c6QAAAP1JREFUSA1jYBgFoyEwGgLDNgT+///PQonnyNJ///4riRt3H1++ce9JKDmW37j3NBmo//TDhx8Ecelnwibxm+FnGFBch+E/wzJSLQdZysjwfzZQv8mPv198sJkPEsNqsZqi7CQGRsYWBgZgcJNgOcxSYDAzMjIwFqsrySzGZTFecaBvm4FB9v/G3Se/CfkcZOnNe0/+gdTfvPukCK/BxEgSYznVLYU5DJ/lNLMUn+WUWMoIM5gYGuRzhv//axgYGP8AU888oJ5UeEJSlukjxgyYGpIsBmlCWA4xApx6SbQUZjnJNCzOqZJ6SbX9zoOnVqTqGVU/GgKjITC8QgAAQhHA2apbODgAAAAASUVORK5CYII="},8880:function(t,e,a){},"95e9":function(t,e,a){"use strict";a.d(e,"l",(function(){return r})),a.d(e,"i",(function(){return i})),a.d(e,"j",(function(){return o})),a.d(e,"k",(function(){return s})),a.d(e,"m",(function(){return l})),a.d(e,"e",(function(){return c})),a.d(e,"d",(function(){return u})),a.d(e,"f",(function(){return d})),a.d(e,"c",(function(){return p})),a.d(e,"n",(function(){return h})),a.d(e,"g",(function(){return g})),a.d(e,"b",(function(){return m})),a.d(e,"a",(function(){return f})),a.d(e,"h",(function(){return b}));var n=a("b775"),r=function(t){return Object(n["a"])("/goodsType/page","get",t)},i=function(t){return Object(n["a"])("/goodsType","post",t)},o=function(t){return Object(n["a"])("/goodsType/".concat(t),"delete",t)},s=function(t){return Object(n["a"])("/goodsType/".concat(t),"get",t)},l=function(t,e){return Object(n["a"])("/goodsType/".concat(t),"put",e)},c=function(t){return Object(n["a"])("/business-hall/courier/page","get",t)},u=function(t){return Object(n["a"])("/business-hall/courier/".concat(t),"get",t)},d=function(t,e){return Object(n["a"])("/business-hall/scope/".concat(t,"/").concat(e),"get",t)},p=function(t){return Object(n["a"])("/business-hall/scope","post",t)},h=function(t){return Object(n["a"])("/pickup-dispatch-task-manager/page","post",t)},g=function(t,e){return Object(n["a"])("/business-hall/scope/".concat(t,"/").concat(e),"delete")},m=function(t){return Object(n["a"])("/truck-return-register/pageQuery","post",t)},f=function(t){return Object(n["a"])("/truck-return-register/detail/".concat(t),"get",t)},b=function(t){return Object(n["a"])("/pickup-dispatch-task-manager/".concat(t.courierId),"put",t.ids)}},aefe:function(t,e,a){t.exports=a.p+"static/img/pic-kong.742d3899.png"},baa0:function(t,e,a){"use strict";var n=a("8880"),r=a.n(n);r.a}}]);