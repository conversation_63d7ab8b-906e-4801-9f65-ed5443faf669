(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6e98a63c","chunk-5b0cb9f7","chunk-dab9ce92"],{"025a":function(t,e,a){"use strict";var n=a("7953"),i=a.n(n);i.a},"0733":function(t,e,a){},"0eee":function(t,e,a){},"1c4c":function(t,e,a){"use strict";var n=a("9b43"),i=a("5ca1"),r=a("4bf8"),s=a("1fa8"),o=a("33a4"),l=a("9def"),c=a("f1ae"),u=a("27ee");i(i.S+i.F*!a("5cc5")((function(t){Array.from(t)})),"Array",{from:function(t){var e,a,i,d,m=r(t),p="function"==typeof this?this:Array,h=arguments.length,f=h>1?arguments[1]:void 0,g=void 0!==f,b=0,v=u(m);if(g&&(f=n(f,h>2?arguments[2]:void 0,2)),void 0==v||p==Array&&o(v))for(e=l(m.length),a=new p(e);e>b;b++)c(a,b,g?f(m[b],b):m[b]);else for(d=v.call(m),a=new p;!(i=d.next()).done;b++)c(a,b,g?s(d,f,[i.value,b],!0):i.value);return a.length=b,a}})},"228d":function(t,e,a){},"279a":function(t,e,a){"use strict";var n=a("0eee"),i=a.n(n);i.a},"35d2":function(t,e,a){},"3ad9":function(t,e,a){"use strict";var n=a("0733"),i=a.n(n);i.a},"3fb7":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"dashboard-container dispatch-task customer-list-box",staticStyle:{width:"100%"}},[n("div",{staticClass:"app-container"},[n("el-card",{staticClass:"search-card-box",attrs:{shadow:"never"}},[n("el-form",{ref:"operationalSearchFormData",attrs:{model:t.operationalSearchFormData,"label-width":"80px"}},[n("div",{staticClass:"filter-container"},[n("el-row",{attrs:{gutter:60}},[n("el-col",{attrs:{span:8}},[n("el-form-item",{staticStyle:{"margin-bottom":"0px"},attrs:{label:"派件任务编号:","label-width":"110px"}},[n("el-input",{attrs:{placeholder:"请输入派件任务编号",clearable:""},model:{value:t.operationalSearchFormData.id,callback:function(e){t.$set(t.operationalSearchFormData,"id",e)},expression:"operationalSearchFormData.id"}})],1)],1),t._v(" "),n("el-col",{attrs:{span:8}},[n("el-form-item",{staticStyle:{"margin-bottom":"0px"},attrs:{label:"订单编号:","label-width":"110px"}},[n("el-input",{attrs:{placeholder:"请输入订单编号",clearable:""},model:{value:t.operationalSearchFormData.orderId,callback:function(e){t.$set(t.operationalSearchFormData,"orderId",e)},expression:"operationalSearchFormData.orderId"}})],1)],1),t._v(" "),n("el-col",{attrs:{span:8}},[n("el-form-item",{staticStyle:{"margin-bottom":"20px"},attrs:{label:"所属营业部:","label-width":"110px"}},[n("treeselect",{staticStyle:{width:"100%"},attrs:{options:t.agencyOptions,normalizer:t.normalizer,searchable:!0,placeholder:"请选择所属营业部","no-options-text":"暂无数据","no-results-text":"暂无数据"},on:{select:t.selectAgency,open:t.open,close:t.close},model:{value:t.operationalSearchFormData.agencyId,callback:function(e){t.$set(t.operationalSearchFormData,"agencyId",e)},expression:"operationalSearchFormData.agencyId"}}),t._v(" "),n("img",{ref:"arrow",staticStyle:{position:"absolute",width:"18px",height:"18px",right:"9px",top:"12px",cursor:"pointer","pointer-events":"none","transition-duration":"0.3s"},attrs:{src:a("5ae1")}})],1)],1),t._v(" "),n("el-col",{attrs:{span:8}},[n("el-form-item",{staticStyle:{"margin-bottom":"15px"},attrs:{label:"签收状态:","label-width":"110px"}},[n("el-select",{attrs:{clearable:"",placeholder:"请选择"},model:{value:t.operationalSearchFormData.signStatus,callback:function(e){t.$set(t.operationalSearchFormData,"signStatus",e)},expression:"operationalSearchFormData.signStatus"}},t._l(t.signStatus,(function(t){return n("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1)],1),t._v(" "),n("el-col",{attrs:{span:8}},[n("el-form-item",{staticStyle:{"margin-bottom":"20px"},attrs:{label:"预计完成时间:","label-width":"110px"}},[n("el-date-picker",{attrs:{format:"yyyy-MM-dd HH:mm:ss","value-format":"yyyy-MM-dd HH:mm:ss",type:"datetimerange",placeholder:"选择预计完成时间","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间"},on:{change:t.handleEstimatedTimeChange},model:{value:t.estimatedTime,callback:function(e){t.estimatedTime=e},expression:"estimatedTime"}})],1)],1),t._v(" "),n("el-col",{attrs:{span:8}},[n("el-form-item",{staticStyle:{"margin-bottom":"20px"},attrs:{label:"实际完成时间:","label-width":"110px"}},[n("el-date-picker",{attrs:{format:"yyyy-MM-dd HH:mm:ss","value-format":"yyyy-MM-dd HH:mm:ss",type:"datetimerange",placeholder:"选择预计完成时间","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间"},on:{change:t.handleActualTimeChange},model:{value:t.actualTime,callback:function(e){t.actualTime=e},expression:"actualTime"}})],1)],1),t._v(" "),n("el-col",{attrs:{span:8}},[n("el-form-item",{staticStyle:{"margin-bottom":"20px"},attrs:{label:"任务分配状态:","label-width":"110px"}},[n("el-select",{attrs:{clearable:"",placeholder:"请选择任务分配状态"},model:{value:t.operationalSearchFormData.assignedStatus,callback:function(e){t.$set(t.operationalSearchFormData,"assignedStatus",e)},expression:"operationalSearchFormData.assignedStatus"}},t._l(t.operationOptions,(function(t){return n("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1)],1),t._v(" "),n("el-col",{attrs:{span:8}},[n("el-button",{attrs:{type:"warning"},on:{click:function(e){return t.handleFilter("查询")}}},[t._v("搜索")]),t._v(" "),n("el-button",{staticClass:"reset-btn",attrs:{plain:""},on:{click:function(e){return t.resetForm("operationalSearchFormData")}}},[t._v("重置")])],1)],1)],1)])],1),t._v(" "),n("TabChange",{attrs:{"default-active-index":0,"is-show-num":!1,"setting-list":[{key:"all",value:0,label:"全部",num:t.allNum},{key:"able",value:1,label:"新任务",num:t.newNum},{key:"unable",value:2,label:"已完成",num:t.doneNum}]},on:{tabChange:t.tabChange}}),t._v(" "),n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],class:{"loading-box":t.listLoading},staticStyle:{"margin-top":"20px"},attrs:{"element-loading-text":"加载中"}},[n("el-card",{staticClass:"table-card-box",attrs:{shadow:"never"}},[n("el-button",{staticClass:"customer-add-btn",on:{click:function(e){return t.handleAddCourier(t.multipleSelection)}}},[t._v("人工分配")]),t._v(" "),n("el-table",{key:t.tableKey,ref:"multipleTable",staticStyle:{width:"100%"},attrs:{data:t.dataList,fit:"",stripe:"","header-cell-style":{background:"rgba(250,252,255,1)"},"header-cell-class-name":t.isTableDisabled?"disabled":"able"},on:{"selection-change":t.handleSelectionChange}},[n("div",{directives:[{name:"show",rawName:"v-show",value:(!t.dataList||t.dataList.length<=0)&&!t.listLoading&&!t.searchkey,expression:"(!dataList || dataList.length <= 0) && !listLoading && !searchkey"}],attrs:{slot:"empty"},slot:"empty"},[n("img",{staticStyle:{"margin-top":"20px",width:"25%",height:"25%"},attrs:{src:a("aefe"),alt:"img"}}),t._v(" "),n("p",{staticStyle:{"margin-top":"-20px","padding-bottom":"0px"}},[t._v("这里空空如也")])]),t._v(" "),n("el-card",{directives:[{name:"show",rawName:"v-show",value:(!t.dataList||t.dataList.length<=0)&&!t.listLoading&&t.searchkey,expression:"(!dataList || dataList.length <= 0) && !listLoading && searchkey"}],staticClass:"table-empty-box",attrs:{slot:"empty",shadow:"never"},slot:"empty"},[n("empty")],1),t._v(" "),n("el-table-column",{attrs:{align:"left",type:"selection",width:"60",fixed:"",selectable:t.selectEnable}}),t._v(" "),n("el-table-column",{attrs:{align:"left",type:"index",label:"序号",width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(e.$index+(t.operationalSearchFormData.page-1)*t.operationalSearchFormData.pageSize+1))])]}}])}),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"派件作业编号","min-width":"160px",prop:"id"}}),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"订单编号","min-width":"180",prop:"orderId"}}),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"所在营业部","min-width":"180",prop:"agencyName"}}),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"任务分配状态","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[1==e.row.assignedStatus?n("span",[t._v("未分配")]):2==e.row.assignedStatus?n("span",{staticClass:"tableColumn-status normal"},[t._v("已分配")]):n("span",{staticClass:"tableColumn-status stop-use"},[t._v("待人工分配")])]}}])}),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"快递员姓名","min-width":"120",prop:"courierName"}}),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"签收状态","min-width":"80px"},scopedSlots:t._u([{key:"default",fn:function(e){return[1===e.row.signStatus?n("span",[t._v("已签收")]):e.row.signStatus?n("span",[t._v("拒收")]):n("span",[t._v("未签收")])]}}])}),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"签收人","min-width":"100",prop:"signRecipient"},scopedSlots:t._u([{key:"default",fn:function(e){return[1===e.row.signRecipient?n("span",[t._v("本人")]):n("span",[t._v("代收")])]}}])}),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"预计开始时间","min-width":"160",prop:"estimatedStartTime"}}),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"预计完成时间","min-width":"160",prop:"estimatedEndTime"}}),t._v(" "),[1,3].includes(t.operationalSearchFormData.status)?t._e():n("el-table-column",{attrs:{align:"left",label:"实际完成时间","min-width":"160",prop:"actualEndTime"}})],1),t._v(" "),n("div",{staticClass:"pagination",staticStyle:{"margin-bottom":"30px!important"}},[n("div",{staticClass:"pages"},[n("el-pagination",{attrs:{"current-page":Number(t.operationalSearchFormData.page),total:Number(t.total),"page-size":Number(t.operationalSearchFormData.pageSize),"page-sizes":[10,20,30,50],layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)]),t._v(" "),n("el-dialog",{staticClass:"add-form-dialog",attrs:{title:t.titleInfo.text,visible:t.dialogFormVisible,width:"600px"},on:{"update:visible":function(e){t.dialogFormVisible=e}}},[n("el-form",{ref:"dataForm"},[n("el-form-item",{attrs:{label:"选择快递员：",prop:"courierName","label-width":t.formLabelWidth}},[n("el-select",{attrs:{"value-key":"item.userId",placeholder:"请选择",clearable:"",filterable:""},on:{change:t.handleChange},model:{value:t.courierId,callback:function(e){t.courierId=e},expression:"courierId"}},t._l(t.courierOptions,(function(t){return n("el-option",{key:t.userId,attrs:{label:t.name,value:t.userId}})})),1)],1)],1),t._v(" "),n("div",{staticClass:"dialog-footer",staticStyle:{"text-align":"center"},attrs:{slot:"footer"},slot:"footer"},[n("el-button",{staticClass:"save-btn",attrs:{type:"primary"},on:{click:t.createData}},[t._v("确定")]),t._v(" "),n("el-button",{staticClass:"cancel-btn",on:{click:t.cancelDisFn}},[t._v("取消")])],1)],1)],1)],1)],1)])},i=[],r=(a("ac6a"),a("4f7f"),a("5df3"),a("1c4c"),a("96cf"),a("3b8d")),s=(a("7f7f"),a("95e9")),o=(a("542c"),a("ca17")),l=a.n(o),c=a("b57e"),u=a("5850"),d=a("ed08"),m={components:{Treeselect:l.a,TabChange:c["a"]},data:function(){return{isTableDisabled:!1,allNum:0,newNum:0,doneNum:0,courierId:"",formLabelWidth:"100px",multipleSelection:[],tableKey:0,dataList:[],total:null,searchkey:!1,provinceList:[],tableChecked:[],courierOptions:[],operationOptions:[{id:2,name:"已分配"},{id:3,name:"待人工分配"}],signStatus:[{id:1,name:"已签收"},{id:2,name:"拒收"}],listLoading:!0,alertText:"",dialogFormVisible:!1,operationalSearchFormData:{page:1,pageSize:10,taskType:2,status:0,id:"",orderId:"",agencyId:"",minEstimatedEndTime:"",maxEstimatedEndTime:"",minActualEndTime:"",maxActualEndTime:"",assignedStatus:"",signStatus:""},actualTime:[],estimatedTime:[],courierParams:{page:1,pageSize:1e3,agentId:""},titleInfo:{pageTitle:"",text:""},selectedAgencyList:[],agencyOptions:[],normalizer:function(t){return{id:t.id,label:t.name,children:t.children}}}},mounted:function(){},created:function(){this.getAgencyList(),this.initialDate(),document.onkeydown=function(t){var e=window.event.keyCode;13===e&&this.handleFilter(this.operationalSearchFormData)}},updated:function(){},methods:{selectEnable:function(t){return 2!==t.assignedStatus},getAgencyList:function(){var t=Object(r["a"])(regeneratorRuntime.mark((function t(){var e,a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(u["a"])();case 2:e=t.sent,a=e.data,this.agencyOptions=JSON.parse(a);case 5:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),tabChange:function(t){this.operationalSearchFormData.status=t,this.getList()},selectAgency:function(t){this.operationalSearchFormData.agencyId=t.id},handleActualTimeChange:function(t){this.actualTime=t,this.operationalSearchFormData.minActualEndTime=t[0],this.operationalSearchFormData.maxActualEndTime=t[1]},handleEstimatedTimeChange:function(t){this.estimatedTime=t,this.operationalSearchFormData.minEstimatedEndTime=t[0],this.operationalSearchFormData.maxEstimatedEndTime=t[1]},getLineList:function(){var t=Object(r["a"])(regeneratorRuntime.mark((function t(){var e,a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(s["e"])(this.courierParams);case 2:e=t.sent,a=e.data,this.courierOptions=a.items;case 5:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),opens:function(){this.$refs.arrows.style.transform="rotate(-180deg)"},closes:function(){this.$refs.arrows.style.transform="rotate(0deg)"},open:function(){this.$refs.arrow.style.transform="rotate(-180deg)"},close:function(){this.$refs.arrow.style.transform="rotate(0deg)"},cancelDisFn:function(){this.$refs["dataForm"].resetFields(),this.dialogFormVisible=!1},initialDate:function(){this.getList()},handleSelectionChange:function(t){this.multipleSelection=t.map((function(t){return t.id})),this.selectedAgencyList=Array.from(new Set(t.map((function(t){return t.agencyId})))),this.courierParams.agentId||this.getLineList(),this.courierParams.agentId=this.selectedAgencyList[0]},getList:function(){var t=Object(r["a"])(regeneratorRuntime.mark((function t(){var e,a,n;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return this.listLoading=!0,e=Object(d["b"])(this.operationalSearchFormData),t.next=4,Object(s["n"])(e);case 4:a=t.sent,n=a.data,this.listLoading=!1,this.dataList=n.items,this.isTableDisabled=this.dataList.every((function(t){return 2===t.assignedStatus})),this.total=n.counts;case 10:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),resetForm:function(){this.$refs["operationalSearchFormData"].resetFields(),this.operationalSearchFormData.page=1,this.operationalSearchFormData.pageSize=10,this.operationalSearchFormData.taskType=2,this.operationalSearchFormData.id="",this.operationalSearchFormData.orderId="",this.operationalSearchFormData.agencyId="",this.operationalSearchFormData.minEstimatedEndTime="",this.searchkey=!1,this.operationalSearchFormData.maxEstimatedEndTime="",this.operationalSearchFormData.minActualEndTime="",this.operationalSearchFormData.maxActualEndTime="",this.operationalSearchFormData.assignedStatus="",this.operationalSearchFormData.signStatus="",this.actualTime=[],this.estimatedTime=[],this.getList()},handleFilter:function(t){this.operationalSearchFormData.page=1;var e=Object(d["b"])(this.operationalSearchFormData);this.getList(e),this.searchkey=!0},handleSizeChange:function(t){this.operationalSearchFormData.pageSize=t,1===this.operationalSearchFormData.page&&this.getList(this.operationalSearchFormData)},handleCurrentChange:function(t){this.operationalSearchFormData.page=t,this.getList()},handleChange:function(t){this.courierId=t},handleAddCourier:function(){var t=this.multipleSelection.length;0!==t?this.selectedAgencyList.length>1?this.$message({message:"请选择一个营业部下面的派件作业！",type:"error"}):(this.dialogFormVisible=!0,this.titleInfo.text="人工分配"):this.$message({message:"选择要分配的作业！",type:"error"})},dialogFormH:function(){this.dialogFormVisible=!1},createData:function(){var t=Object(r["a"])(regeneratorRuntime.mark((function t(){var e=this;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.courierId){t.next=2;break}return t.abrupt("return",this.$message.error("请选择快递员"));case 2:return this.dialogFormH(),t.next=5,Object(s["h"])({ids:this.multipleSelection,courierId:this.courierId}).then((function(t){"200"===String(t.code)&&(e.$message.success("人工分配成功"),e.getList(e.operationalSearchFormData),e.courierId="",e.multipleSelection=[],e.selectedAgencyList=[]),e.$refs.multipleTable.clearSelection()})).catch((function(t){e.courierId="",e.multipleSelection=[],e.selectedAgencyList=[],e.$refs.multipleTable.clearSelection(),e.$message.error(t.msg||"人工分配失败")}));case 5:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),handleWayllDetail:function(t){this.$router.push({path:"/business/waybill-detail",query:{id:t}})}}},p=m,h=(a("3ad9"),a("279a"),a("2877")),f=Object(h["a"])(p,n,i,!1,null,"b3f75f4a",null);e["default"]=f.exports},"4f7f":function(t,e,a){"use strict";var n=a("c26b"),i=a("b39a"),r="Set";t.exports=a("e0b8")(r,(function(t){return function(){return t(this,arguments.length>0?arguments[0]:void 0)}}),{add:function(t){return n.def(i(this,r),t=0===t?0:t,t)}},n)},5038:function(t,e,a){"use strict";var n=a("e712"),i=a.n(n);i.a},"53ea":function(t,e,a){"use strict";var n=a("35d2"),i=a.n(n);i.a},5850:function(t,e,a){"use strict";a.d(e,"a",(function(){return i})),a.d(e,"b",(function(){return r})),a.d(e,"c",(function(){return s})),a.d(e,"d",(function(){return o}));var n=a("b775"),i=function(t){return Object(n["a"])("/business-hall/tree","get",t)},r=function(t){return Object(n["a"])("/business-hall/".concat(t),"get",t)},s=function(t){return Object(n["a"])("/business-hall/user/page","get",t)},o=function(t){return Object(n["a"])("/business-hall","post",t)}},"5ae1":function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAcCAYAAAB2+A+pAAAAAXNSR0IArs4c6QAAAP1JREFUSA1jYBgFoyEwGgLDNgT+///PQonnyNJ///4riRt3H1++ce9JKDmW37j3NBmo//TDhx8Ecelnwibxm+FnGFBch+E/wzJSLQdZysjwfzZQv8mPv198sJkPEsNqsZqi7CQGRsYWBgZgcJNgOcxSYDAzMjIwFqsrySzGZTFecaBvm4FB9v/G3Se/CfkcZOnNe0/+gdTfvPukCK/BxEgSYznVLYU5DJ/lNLMUn+WUWMoIM5gYGuRzhv//axgYGP8AU888oJ5UeEJSlukjxgyYGpIsBmlCWA4xApx6SbQUZjnJNCzOqZJ6SbX9zoOnVqTqGVU/GgKjITC8QgAAQhHA2apbODgAAAAASUVORK5CYII="},"5df3":function(t,e,a){"use strict";var n=a("02f4")(!0);a("01f9")(String,"String",(function(t){this._t=String(t),this._i=0}),(function(){var t,e=this._t,a=this._i;return a>=e.length?{value:void 0,done:!0}:(t=n(e,a),this._i+=t.length,{value:t,done:!1})}))},"614a":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"dashboard-container cancel-task customer-list-box"},[a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],class:{"loading-box":t.listLoading},staticStyle:{"margin-top":"20px"},attrs:{"element-loading-text":"加载中"}},[a("el-card",{directives:[{name:"show",rawName:"v-show",value:t.dataList&&t.dataList.length>0,expression:"dataList && dataList.length > 0"}],staticClass:"table-card-box",attrs:{shadow:"never"}},[a("el-table",{key:t.tableKey,ref:"multipleTable",staticStyle:{width:"100%"},attrs:{data:t.dataList,fit:"",stripe:"","header-cell-style":{background:"rgba(250,252,255,1)"}},on:{"selection-change":t.handleSelectionChange}},[a("el-table-column",{attrs:{align:"left",type:"index",label:"序号",width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.$index+(t.operationalSearchFormData.page-1)*t.operationalSearchFormData.pageSize+1))])]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"left",label:"作业编号",width:"150"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.id))])]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"left",label:"运单编号",width:"150"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(null===e.row.transportOrder?"":e.row.transportOrder.id))])]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"left",label:"快递员分配状态",width:"140"},scopedSlots:t._u([{key:"default",fn:function(e){return[1==e.row.assignedStatus?a("span",[t._v("未分配")]):2==e.row.assignedStatus?a("span",[t._v("已分配")]):a("span",[t._v("待人工分配")])]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"left",label:"下单时间",width:"160"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(null===e.row.order?"":e.row.order.createTime))])]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"left",label:"作业状态",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[1==e.row.status?a("span",[t._v("待提货")]):2==e.row.status?a("span",[t._v("在途")]):3==e.row.status||4==e.row.status?a("span",[t._v("已交付")]):a("span",[t._v("已作废")])]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"left",label:"快递员姓名",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(null===e.row.courier?"":e.row.courier.name))])]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"left",label:"取消时间",width:"160"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.cancelTime))])]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"left",label:"发件人姓名",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(null===e.row.order?"":e.row.order.senderName))])]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"left",label:"发件人电话",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(null===e.row.order?"":e.row.order.senderPhone))])]}}])}),t._v(" "),a("el-table-column",{attrs:{align:"left",label:"发件人地址","min-width":"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(null===e.row.order?"":e.row.order.senderProvince.name))]),t._v(" "),a("span",[t._v(t._s(null===e.row.order?"":e.row.order.senderCity.name))]),t._v(" "),a("span",[t._v(t._s(null===e.row.order?"":e.row.order.senderCounty.name))]),t._v(" "),a("span",[t._v(t._s(null===e.row.order?"":e.row.order.senderAddress))])]}}])})],1),t._v(" "),a("div",{staticClass:"pagination"},[a("div",{staticClass:"pages"},[a("el-pagination",{attrs:{"current-page":Number(t.operationalSearchFormData.page),total:Number(t.total),"page-size":Number(t.operationalSearchFormData.pageSize),"page-sizes":[10,20,30,50],layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)])],1),t._v(" "),a("el-card",{directives:[{name:"show",rawName:"v-show",value:(!t.dataList||t.dataList.length<=0)&&!t.listLoading,expression:"(!dataList || dataList.length <= 0) && !listLoading"}],staticClass:"table-empty-box",attrs:{shadow:"never"}},[a("empty")],1)],1)])},i=[],r=(a("96cf"),a("3b8d")),s=(a("6b54"),a("95e9")),o={data:function(){return{multipleSelection:[],tableKey:0,dataList:[],total:null,listLoading:!0,alertText:"",operationalSearchFormData:{page:1,pageSize:10,status:5},titleInfo:{pageTitle:"",text:""},formData:{isFamous:"",shortName:"",company:"",tags:"",remarks:""}}},computed:{status:function(t){function e(){return t.apply(this,arguments)}return e.toString=function(){return t.toString()},e}((function(){return status}))},mounted:function(){},created:function(){this.companyId=this.$route.query.companyId,this.initialDate(),document.onkeydown=function(t){var e=window.event.keyCode;13===e&&this.handleFilter(this.operationalSearchFormData)}},updated:function(){},methods:{initialDate:function(){this.getList()},handleSelectionChange:function(t){this.multipleSelection=t},getList:function(){var t=Object(r["a"])(regeneratorRuntime.mark((function t(){var e,a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return this.listLoading=!0,t.next=3,Object(s["n"])(this.operationalSearchFormData);case 3:e=t.sent,a=e.data,this.listLoading=!1,this.dataList=a.items,this.total=a.counts;case 8:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),resetForm:function(){this.$refs["operationalSearchFormData"].resetFields()},handleFilter:function(){this.operationalSearchFormData.page=1,this.getList(this.operationalSearchFormData)},handleSizeChange:function(t){this.operationalSearchFormData.pageSize=t,1===this.operationalSearchFormData.page&&this.getList(this.operationalSearchFormData)},handleCurrentChange:function(t){this.operationalSearchFormData.page=t,this.getList()},handleWayllDetail:function(t){this.$router.push({path:"/business/waybill-detail",query:{id:t}})}}},l=o,c=(a("b7f8"),a("92ca"),a("2877")),u=Object(c["a"])(l,n,i,!1,null,"2046196a",null);e["default"]=u.exports},"620d":function(t,e,a){"use strict";var n=a("c62f"),i=a.n(n);i.a},6624:function(t,e,a){"use strict";var n=a("228d"),i=a.n(n);i.a},7953:function(t,e,a){},"7f28":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"dashboard-container pickup-task customer-list-box"},[n("div",{staticClass:"app-container"},[n("el-card",{staticClass:"search-card-box",attrs:{shadow:"never"}},[n("el-form",{ref:"operationalSearchFormData",staticClass:"demo-form-inline",attrs:{model:t.operationalSearchFormData,"label-width":"80px"}},[n("el-row",{attrs:{gutter:60}},[n("el-col",{attrs:{span:8}},[n("el-form-item",{staticStyle:{"margin-bottom":"0px"},attrs:{label:"取件任务编号:","label-width":"110px"}},[n("el-input",{attrs:{placeholder:"请输入取件任务编号",clearable:""},model:{value:t.operationalSearchFormData.id,callback:function(e){t.$set(t.operationalSearchFormData,"id",e)},expression:"operationalSearchFormData.id"}})],1)],1),t._v(" "),n("el-col",{attrs:{span:8}},[n("el-form-item",{staticStyle:{"margin-bottom":"0px"},attrs:{label:"订单编号:","label-width":"110px"}},[n("el-input",{attrs:{placeholder:"请输入订单编号",clearable:""},model:{value:t.operationalSearchFormData.orderId,callback:function(e){t.$set(t.operationalSearchFormData,"orderId",e)},expression:"operationalSearchFormData.orderId"}})],1)],1),t._v(" "),n("el-col",{attrs:{span:8}},[n("el-form-item",{staticStyle:{"margin-bottom":"20px"},attrs:{label:"所属营业部:","label-width":"110px"}},[n("treeselect",{staticStyle:{width:"100%"},attrs:{options:t.agencyOptions,normalizer:t.normalizer,searchable:!0,placeholder:"请选择所属营业部","no-options-text":"暂无数据","no-results-text":"暂无数据"},on:{select:t.selectAgency,open:t.open,close:t.close},model:{value:t.operationalSearchFormData.agencyId,callback:function(e){t.$set(t.operationalSearchFormData,"agencyId",e)},expression:"operationalSearchFormData.agencyId"}}),t._v(" "),n("img",{ref:"arrow",staticStyle:{position:"absolute",width:"18px",height:"18px",right:"9px",top:"12px",cursor:"pointer","pointer-events":"none","transition-duration":"0.3s"},attrs:{src:a("5ae1")}})],1)],1),t._v(" "),n("el-col",{attrs:{span:8}},[n("el-form-item",{staticStyle:{"margin-bottom":"20px"},attrs:{label:"预计完成时间:","label-width":"110px"}},[n("el-date-picker",{attrs:{format:"yyyy-MM-dd HH:mm:ss","value-format":"yyyy-MM-dd HH:mm:ss",type:"datetimerange",placeholder:"选择预计完成时间","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间"},on:{change:t.handleEstimatedTimeChange},model:{value:t.estimatedTime,callback:function(e){t.estimatedTime=e},expression:"estimatedTime"}})],1)],1),t._v(" "),n("el-col",{attrs:{span:8}},[n("el-form-item",{staticStyle:{"margin-bottom":"20px"},attrs:{label:"实际完成时间:","label-width":"110px"}},[n("el-date-picker",{attrs:{format:"yyyy-MM-dd HH:mm:ss","value-format":"yyyy-MM-dd HH:mm:ss",type:"datetimerange",placeholder:"选择预计完成时间","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间"},on:{change:t.handleActualTimeChange},model:{value:t.actualTime,callback:function(e){t.actualTime=e},expression:"actualTime"}})],1)],1),t._v(" "),n("el-col",{attrs:{span:8}},[n("el-form-item",{staticStyle:{"margin-bottom":"20px"},attrs:{label:"任务分配状态:","label-width":"110px"}},[n("el-select",{attrs:{clearable:"",placeholder:"请选择任务分配状态"},model:{value:t.operationalSearchFormData.assignedStatus,callback:function(e){t.$set(t.operationalSearchFormData,"assignedStatus",e)},expression:"operationalSearchFormData.assignedStatus"}},t._l(t.operationOptions,(function(t){return n("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1)],1),t._v(" "),n("el-col",{attrs:{span:8}},[n("el-button",{attrs:{type:"warning"},on:{click:function(e){return t.handleFilter("查询")}}},[t._v("搜索")]),t._v(" "),n("el-button",{staticClass:"reset-btn",attrs:{plain:""},on:{click:function(e){return t.resetForm("operationalSearchFormData")}}},[t._v("重置")])],1)],1)],1)],1),t._v(" "),n("TabChange",{attrs:{"default-active-index":0,"is-show-num":!1,"setting-list":[{key:"all",value:0,label:"全部",num:t.allNum},{key:"able",value:1,label:"新任务",num:t.newNum},{key:"unable",value:2,label:"已完成",num:t.doneNum},{key:"unable",value:3,label:"已取消",num:t.cancelNum}]},on:{tabChange:t.tabChange}}),t._v(" "),n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],class:{"loading-box":t.listLoading},staticStyle:{"margin-top":"20px"},attrs:{"element-loading-text":"加载中"}},[n("el-card",{staticClass:"table-card-box",attrs:{shadow:"never"}},[n("el-button",{staticClass:"customer-add-btn",on:{click:function(e){return t.handleAddCourier(t.multipleSelection)}}},[t._v("人工分配")]),t._v(" "),n("el-table",{key:t.tableKey,ref:"multipleTable",staticStyle:{width:"100%"},attrs:{data:t.dataList,fit:"",stripe:"","header-cell-style":{background:"rgba(250,252,255,1)"},"header-cell-class-name":t.isTableDisabled?"disabled":"able"},on:{"selection-change":t.handleSelectionChange}},[n("div",{directives:[{name:"show",rawName:"v-show",value:(!t.dataList||t.dataList.length<=0)&&!t.listLoading&&!t.searchkey,expression:"(!dataList || dataList.length <= 0) && !listLoading && !searchkey"}],attrs:{slot:"empty"},slot:"empty"},[n("img",{staticStyle:{"margin-top":"20px",width:"25%",height:"25%"},attrs:{src:a("a586"),alt:"img"}}),t._v(" "),n("p",{staticStyle:{"margin-top":"-20px","padding-bottom":"0px"}},[t._v("这里空空如也")])]),t._v(" "),n("el-card",{directives:[{name:"show",rawName:"v-show",value:(!t.dataList||t.dataList.length<=0)&&!t.listLoading&&t.searchkey,expression:"(!dataList || dataList.length <= 0) && !listLoading && searchkey"}],staticClass:"table-empty-box",attrs:{slot:"empty",shadow:"never"},slot:"empty"},[n("empty")],1),t._v(" "),n("el-table-column",{attrs:{align:"left",type:"selection",width:"60",fixed:"",selectable:t.selectEnable}}),t._v(" "),n("el-table-column",{attrs:{align:"left",type:"index",label:"序号",width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(e.$index+(t.operationalSearchFormData.page-1)*t.operationalSearchFormData.pageSize+1))])]}}])}),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"取件作业编号","min-width":"180",prop:"id"}}),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"所在营业部","min-width":"180",prop:"agencyName"}}),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"任务分配状态","min-width":"110"},scopedSlots:t._u([{key:"default",fn:function(e){return[1==e.row.assignedStatus?n("span",[t._v("未分配")]):2==e.row.assignedStatus?n("span",{staticClass:"tableColumn-status normal"},[t._v("已分配")]):n("span",{staticClass:"tableColumn-status stop-use"},[t._v("待人工分配")])]}}])}),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"快递员姓名","min-width":"120",prop:"courierName"}}),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"订单编号","min-width":"180",prop:"orderId"}}),t._v(" "),3===t.operationalSearchFormData.status?n("el-table-column",{attrs:{align:"left",label:"取消时间","min-width":"180",prop:"cancelTime"}}):t._e(),t._v(" "),3===t.operationalSearchFormData.status?n("el-table-column",{attrs:{align:"left",label:"取消原因","min-width":"180",prop:"cancelReason"}}):t._e(),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"预计开始时间","min-width":"160",prop:"estimatedStartTime"}}),t._v(" "),n("el-table-column",{attrs:{align:"left",label:"预计完成时间","min-width":"160",prop:"estimatedEndTime"}}),t._v(" "),[1,3].includes(t.operationalSearchFormData.status)?t._e():n("el-table-column",{key:"actualEndTime",attrs:{align:"left",label:"实际完成时间","min-width":"160",prop:"actualEndTime"}})],1),t._v(" "),n("div",{staticClass:"pagination"},[n("div",{directives:[{name:"show",rawName:"v-show",value:t.dataList&&t.dataList.length>0,expression:"dataList && dataList.length > 0"}],staticClass:"pages"},[n("el-pagination",{attrs:{"current-page":Number(t.operationalSearchFormData.page),total:Number(t.total),"page-size":Number(t.operationalSearchFormData.pageSize),"page-sizes":[10,20,30,50],layout:"total, sizes, prev, pager, next, jumper"},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)]),t._v(" "),n("el-dialog",{staticClass:"add-form-dialog",attrs:{title:t.titleInfo.text,visible:t.dialogFormVisible,width:"600px"},on:{"update:visible":function(e){t.dialogFormVisible=e}}},[n("el-form",{ref:"dataForm",attrs:{"label-position":"right"}},[n("el-form-item",{attrs:{label:"选择快递员：",prop:"courierName","label-width":t.formLabelWidth}},[n("el-select",{attrs:{"value-key":"item.userId",placeholder:"请选择",clearable:"",filterable:""},on:{change:t.handleChange},model:{value:t.courierId,callback:function(e){t.courierId=e},expression:"courierId"}},t._l(t.courierOptions,(function(t){return n("el-option",{key:t.userId,attrs:{label:t.name,value:t.userId}})})),1)],1)],1),t._v(" "),n("div",{staticClass:"dialog-footer",staticStyle:{"text-align":"center"},attrs:{slot:"footer"},slot:"footer"},[n("el-button",{staticClass:"save-btn",on:{click:t.createData}},[t._v("确定")]),t._v(" "),n("el-button",{staticClass:"cancel-btn",on:{click:t.cancelFn}},[t._v("取消")])],1)],1)],1)],1)],1)])},i=[],r=(a("ac6a"),a("4f7f"),a("5df3"),a("1c4c"),a("96cf"),a("3b8d")),s=(a("6b54"),a("7f7f"),a("95e9")),o=a("ed08"),l=a("ca17"),c=a.n(l),u=a("b57e"),d=a("5850"),m=(a("542c"),{components:{Treeselect:c.a,TabChange:u["a"]},props:{getNewDisabled:Boolean},data:function(){return{isTableDisabled:!1,allNum:0,newNum:0,doneNum:0,cancelNum:0,courierId:"",formLabelWidth:"100px",multipleSelection:[],tableKey:0,dataList:[],cityList:[],searchkey:!1,courierOptions:{},total:null,operationOptions:[{id:2,name:"已分配"},{id:3,name:"待人工分配"}],listLoading:!0,dialogFormVisible:!1,operationalSearchFormData:{page:1,pageSize:10,taskType:1,id:"",orderId:"",agencyId:"",status:0,minEstimatedEndTime:"",maxEstimatedEndTime:"",minActualEndTime:"",maxActualEndTime:"",assignedStatus:""},actualTime:[],estimatedTime:[],courierParams:{page:1,pageSize:1e3,agentId:""},titleInfo:{pageTitle:"",text:""},selectedAgencyList:[],agencyOptions:[],normalizer:function(t){return{id:t.id,label:t.name,children:t.children}}}},computed:{status:function(t){function e(){return t.apply(this,arguments)}return e.toString=function(){return t.toString()},e}((function(){return status}))},mounted:function(){},created:function(){this.getAgencyList(),this.initialDate(),document.onkeydown=function(t){var e=window.event.keyCode;13===e&&this.handleFilter(this.operationalSearchFormData)}},updated:function(){},methods:{selectEnable:function(t){return 2!==t.assignedStatus},tabChange:function(t){t!==this.operationalSearchFormData.status&&(this.operationalSearchFormData.status=t,this.getList())},selectAgency:function(t){this.operationalSearchFormData.agencyId=t.id},handleActualTimeChange:function(t){this.actualTime=t,this.operationalSearchFormData.minActualEndTime=t[0],this.operationalSearchFormData.maxActualEndTime=t[1]},handleEstimatedTimeChange:function(t){this.estimatedTime=t,this.operationalSearchFormData.minEstimatedEndTime=t[0],this.operationalSearchFormData.maxEstimatedEndTime=t[1]},opens:function(){this.$refs.arrows.style.transform="rotate(-180deg)"},closes:function(){this.$refs.arrows.style.transform="rotate(0deg)"},open:function(){this.$refs.arrow.style.transform="rotate(-180deg)"},close:function(){this.$refs.arrow.style.transform="rotate(0deg)"},getAgencyList:function(){var t=Object(r["a"])(regeneratorRuntime.mark((function t(){var e,a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(d["a"])();case 2:e=t.sent,a=e.data,this.agencyOptions=JSON.parse(a);case 5:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),handleChange:function(t){this.courierId=t},cancelFn:function(){this.dialogFormVisible=!1,this.$refs["dataForm"].resetFields()},initialDate:function(){this.getList()},handleSelectionChange:function(t){this.multipleSelection=t.map((function(t){return t.id})),this.selectedAgencyList=Array.from(new Set(t.map((function(t){return t.agencyId})))),this.courierParams.agentId||this.getLineList(),this.courierParams.agentId=this.selectedAgencyList[0]},getLineList:function(){var t=Object(r["a"])(regeneratorRuntime.mark((function t(){var e,a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(s["e"])(this.courierParams);case 2:e=t.sent,a=e.data,this.courierOptions=a.items;case 5:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),getList:function(){var t=Object(r["a"])(regeneratorRuntime.mark((function t(){var e,a,n;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return this.listLoading=!0,e=Object(o["b"])(this.operationalSearchFormData),t.next=4,Object(s["n"])(e);case 4:a=t.sent,n=a.data,this.listLoading=!1,this.dataList=n.items,this.isTableDisabled=this.dataList.every((function(t){return 2===t.assignedStatus})),this.total=n.counts;case 10:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),handleFilter:function(t){this.operationalSearchFormData.page=1;var e=Object(o["b"])(this.operationalSearchFormData);this.getList(e),this.searchkey=!0},resetForm:function(t){this.$refs[t].resetFields(),this.operationalSearchFormData.page=1,this.operationalSearchFormData.pageSize=10,this.operationalSearchFormData.taskType=1,this.operationalSearchFormData.id="",this.operationalSearchFormData.orderId="",this.operationalSearchFormData.agencyId="",this.operationalSearchFormData.minEstimatedEndTime="",this.searchkey=!1,this.operationalSearchFormData.maxEstimatedEndTime="",this.operationalSearchFormData.minActualEndTime="",this.operationalSearchFormData.maxActualEndTime="",this.operationalSearchFormData.assignedStatus="",this.actualTime=[],this.estimatedTime=[],this.getList()},handleSizeChange:function(t){this.operationalSearchFormData.pageSize=t,1===this.operationalSearchFormData.page&&this.getList(this.operationalSearchFormData)},handleCurrentChange:function(t){this.operationalSearchFormData.page=t,this.getList()},handleAddCourier:function(){var t=this.multipleSelection.length;0!==t?this.selectedAgencyList.length>1?this.$message({message:"请选择一个营业部下面的取件作业！",type:"error"}):(this.dialogFormVisible=!0,this.titleInfo.text="人工分配"):this.$message({message:"选择要分配的作业！",type:"error"})},dialogFormH:function(){this.dialogFormVisible=!1},createData:function(){var t=Object(r["a"])(regeneratorRuntime.mark((function t(){var e=this;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.courierId){t.next=2;break}return t.abrupt("return",this.$message.error("请选择快递员"));case 2:return this.dialogFormH(),t.next=5,Object(s["h"])({ids:this.multipleSelection,courierId:this.courierId}).then((function(t){"200"===String(t.code)&&(e.$message.success("人工分配成功"),e.getList(e.operationalSearchFormData),e.courierId="",e.multipleSelection=[],e.selectedAgencyList=[]),e.$refs.multipleTable.clearSelection()})).catch((function(t){e.courierId="",e.multipleSelection=[],e.selectedAgencyList=[],e.$refs.multipleTable.clearSelection(),e.$message.error(t.msg||"人工分配失败")}));case 5:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),handleWayllDetail:function(t){this.$router.push({path:"/business/waybill-detail",query:{id:t}})}}}),p=m,h=(a("6624"),a("025a"),a("2877")),f=Object(h["a"])(p,n,i,!1,null,"6fe545db",null);e["default"]=f.exports},"92ca":function(t,e,a){"use strict";var n=a("e73a"),i=a.n(n);i.a},"95e9":function(t,e,a){"use strict";a.d(e,"l",(function(){return i})),a.d(e,"i",(function(){return r})),a.d(e,"j",(function(){return s})),a.d(e,"k",(function(){return o})),a.d(e,"m",(function(){return l})),a.d(e,"e",(function(){return c})),a.d(e,"d",(function(){return u})),a.d(e,"f",(function(){return d})),a.d(e,"c",(function(){return m})),a.d(e,"n",(function(){return p})),a.d(e,"g",(function(){return h})),a.d(e,"b",(function(){return f})),a.d(e,"a",(function(){return g})),a.d(e,"h",(function(){return b}));var n=a("b775"),i=function(t){return Object(n["a"])("/goodsType/page","get",t)},r=function(t){return Object(n["a"])("/goodsType","post",t)},s=function(t){return Object(n["a"])("/goodsType/".concat(t),"delete",t)},o=function(t){return Object(n["a"])("/goodsType/".concat(t),"get",t)},l=function(t,e){return Object(n["a"])("/goodsType/".concat(t),"put",e)},c=function(t){return Object(n["a"])("/business-hall/courier/page","get",t)},u=function(t){return Object(n["a"])("/business-hall/courier/".concat(t),"get",t)},d=function(t,e){return Object(n["a"])("/business-hall/scope/".concat(t,"/").concat(e),"get",t)},m=function(t){return Object(n["a"])("/business-hall/scope","post",t)},p=function(t){return Object(n["a"])("/pickup-dispatch-task-manager/page","post",t)},h=function(t,e){return Object(n["a"])("/business-hall/scope/".concat(t,"/").concat(e),"delete")},f=function(t){return Object(n["a"])("/truck-return-register/pageQuery","post",t)},g=function(t){return Object(n["a"])("/truck-return-register/detail/".concat(t),"get",t)},b=function(t){return Object(n["a"])("/pickup-dispatch-task-manager/".concat(t.courierId),"put",t.ids)}},a586:function(t,e,a){t.exports=a.p+"static/img/<EMAIL>"},aefe:function(t,e,a){t.exports=a.p+"static/img/pic-kong.742d3899.png"},b39a:function(t,e,a){var n=a("d3f4");t.exports=function(t,e){if(!n(t)||t._t!==e)throw TypeError("Incompatible receiver, "+e+" required!");return t}},b57e:function(t,e,a){"use strict";var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-card",{staticClass:"tabchange-card",attrs:{shadow:"never"}},[a("div",{staticClass:"tab-change"},t._l(t.settingList,(function(e){return a("div",{key:e.value,staticClass:"tab-item",class:{active:e.value==t.activeIndex},on:{click:function(a){return t.tabChange(e.value)}}},[a("span",{staticClass:"status"},[t._v(t._s(e.label))]),t._v(" "),t.isShowNum?a("span",{staticClass:"status-num",staticStyle:{"font-weight":"bold"}},[t._v(t._s(e.num))]):t._e()])})),0)])},i=[],r=(a("c5f6"),{name:"TabChange",props:{defaultActiveIndex:{type:Number,required:!0},settingList:{type:Array,required:!0},isShowNum:{type:Boolean,default:!0}},data:function(){return{activeIndex:this._props.defaultActiveIndex||0}},methods:{tabChange:function(t){this.activeIndex=t,this.$emit("tabChange",t)}}}),s=r,o=(a("53ea"),a("2877")),l=Object(o["a"])(s,n,i,!1,null,null,null);e["a"]=l.exports},b7f8:function(t,e,a){"use strict";var n=a("f40b"),i=a.n(n);i.a},c26b:function(t,e,a){"use strict";var n=a("86cc").f,i=a("2aeb"),r=a("dcbc"),s=a("9b43"),o=a("f605"),l=a("4a59"),c=a("01f9"),u=a("d53b"),d=a("7a56"),m=a("9e1e"),p=a("67ab").fastKey,h=a("b39a"),f=m?"_s":"size",g=function(t,e){var a,n=p(e);if("F"!==n)return t._i[n];for(a=t._f;a;a=a.n)if(a.k==e)return a};t.exports={getConstructor:function(t,e,a,c){var u=t((function(t,n){o(t,u,e,"_i"),t._t=e,t._i=i(null),t._f=void 0,t._l=void 0,t[f]=0,void 0!=n&&l(n,a,t[c],t)}));return r(u.prototype,{clear:function(){for(var t=h(this,e),a=t._i,n=t._f;n;n=n.n)n.r=!0,n.p&&(n.p=n.p.n=void 0),delete a[n.i];t._f=t._l=void 0,t[f]=0},delete:function(t){var a=h(this,e),n=g(a,t);if(n){var i=n.n,r=n.p;delete a._i[n.i],n.r=!0,r&&(r.n=i),i&&(i.p=r),a._f==n&&(a._f=i),a._l==n&&(a._l=r),a[f]--}return!!n},forEach:function(t){h(this,e);var a,n=s(t,arguments.length>1?arguments[1]:void 0,3);while(a=a?a.n:this._f){n(a.v,a.k,this);while(a&&a.r)a=a.p}},has:function(t){return!!g(h(this,e),t)}}),m&&n(u.prototype,"size",{get:function(){return h(this,e)[f]}}),u},def:function(t,e,a){var n,i,r=g(t,e);return r?r.v=a:(t._l=r={i:i=p(e,!0),k:e,v:a,p:n=t._l,n:void 0,r:!1},t._f||(t._f=r),n&&(n.n=r),t[f]++,"F"!==i&&(t._i[i]=r)),t},getEntry:g,setStrong:function(t,e,a){c(t,e,(function(t,a){this._t=h(t,e),this._k=a,this._l=void 0}),(function(){var t=this,e=t._k,a=t._l;while(a&&a.r)a=a.p;return t._t&&(t._l=a=a?a.n:t._t._f)?u(0,"keys"==e?a.k:"values"==e?a.v:[a.k,a.v]):(t._t=void 0,u(1))}),a?"entries":"values",!a,!0),d(e)}}},c62f:function(t,e,a){},e0b8:function(t,e,a){"use strict";var n=a("7726"),i=a("5ca1"),r=a("2aba"),s=a("dcbc"),o=a("67ab"),l=a("4a59"),c=a("f605"),u=a("d3f4"),d=a("79e5"),m=a("5cc5"),p=a("7f20"),h=a("5dbc");t.exports=function(t,e,a,f,g,b){var v=n[t],y=v,S=g?"set":"add",w=y&&y.prototype,_={},x=function(t){var e=w[t];r(w,t,"delete"==t?function(t){return!(b&&!u(t))&&e.call(this,0===t?0:t)}:"has"==t?function(t){return!(b&&!u(t))&&e.call(this,0===t?0:t)}:"get"==t?function(t){return b&&!u(t)?void 0:e.call(this,0===t?0:t)}:"add"==t?function(t){return e.call(this,0===t?0:t),this}:function(t,a){return e.call(this,0===t?0:t,a),this})};if("function"==typeof y&&(b||w.forEach&&!d((function(){(new y).entries().next()})))){var F=new y,k=F[S](b?{}:-0,1)!=F,D=d((function(){F.has(1)})),C=m((function(t){new y(t)})),L=!b&&d((function(){var t=new y,e=5;while(e--)t[S](e,e);return!t.has(-0)}));C||(y=e((function(e,a){c(e,y,t);var n=h(new v,e,y);return void 0!=a&&l(a,g,n[S],n),n})),y.prototype=w,w.constructor=y),(D||L)&&(x("delete"),x("has"),g&&x("get")),(L||k)&&x(S),b&&w.clear&&delete w.clear}else y=f.getConstructor(e,t,g,S),s(y.prototype,a),o.NEED=!0;return p(y,t),_[t]=y,i(i.G+i.W+i.F*(y!=v),_),b||f.setStrong(y,t,g),y}},e712:function(t,e,a){},e73a:function(t,e,a){},ed09:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"dashboard-container operational"},[a("div",{staticClass:"app-container"},[a("el-card",{staticClass:"operation-box",attrs:{shadow:"never"}},t._l(t.menu,(function(e,n){return a("div",{key:n,staticClass:"operation-item",class:{active:t.isActive==e.value},on:{click:function(a){t.isActive=e.value,t.currentComp=e.comp}}},[t._v("\n        "+t._s(e.name)+"\n      ")])})),0),t._v(" "),a("el-container",[a(t.currentComp,{tag:"test1"})],1)],1)])},i=[],r=(a("6b54"),a("7f28")),s=a("3fb7"),o=a("614a"),l={name:"Courses",components:{pickUptask:r["default"],dispatchTask:s["default"],cancelTask:o["default"]},data:function(){return{city:"",value:"",currentComp:"pickUptask",isActive:"1",id:"",menu:[{name:"取件作业",value:"1",comp:"pickUptask"},{name:"派件作业",value:"2",comp:"dispatchTask"}]}},computed:{status:function(t){function e(){return t.apply(this,arguments)}return e.toString=function(){return t.toString()},e}((function(){return status}))},mounted:function(){},created:function(){},updated:function(){},methods:{}},c=l,u=(a("620d"),a("5038"),a("2877")),d=Object(u["a"])(c,n,i,!1,null,"2ab207d4",null);e["default"]=d.exports},f1ae:function(t,e,a){"use strict";var n=a("86cc"),i=a("4630");t.exports=function(t,e,a){e in t?n.f(t,e,i(0,a)):t[e]=a}},f40b:function(t,e,a){}}]);