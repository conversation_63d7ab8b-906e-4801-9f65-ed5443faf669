<template>
  <div class="htmleaf-container">
    <div
      id="p1_barPie"
      data-to-value="80"
      data-items-count="32"
      class="barPie barPie--radio"
    >
      <span class="barPie__value">0</span>
      <div class="barPie__ring">
        <input
          id="p1_barPieItem31"
          type="radio"
          name="barPieRadioGroup"
          value="100"
          hidden="hidden"
        />
        <label
          for="p1_barPieItem31"
          class="barPie__ring__item"
        >
          <div></div>
        </label>
        <input
          id="p1_barPieItem30"
          type="radio"
          name="barPieRadioGroup"
          value="96.875"
          hidden="hidden"
        />
        <label
          for="p1_barPieItem30"
          class="barPie__ring__item"
        >
          <div></div>
        </label>
        <input
          id="p1_barPieItem29"
          type="radio"
          name="barPieRadioGroup"
          value="93.75"
          hidden="hidden"
        />
        <label
          for="p1_barPieItem29"
          class="barPie__ring__item"
        >
          <div></div>
        </label>
        <input
          id="p1_barPieItem28"
          type="radio"
          name="barPieRadioGroup"
          value="90.625"
          hidden="hidden"
        />
        <label
          for="p1_barPieItem28"
          class="barPie__ring__item"
        >
          <div></div>
        </label>
        <input
          id="p1_barPieItem27"
          type="radio"
          name="barPieRadioGroup"
          value="87.5"
          hidden="hidden"
        />
        <label
          for="p1_barPieItem27"
          class="barPie__ring__item"
        >
          <div></div>
        </label>
        <input
          id="p1_barPieItem26"
          type="radio"
          name="barPieRadioGroup"
          value="84.375"
          hidden="hidden"
        />
        <label
          for="p1_barPieItem26"
          class="barPie__ring__item"
        >
          <div></div>
        </label>
        <input
          id="p1_barPieItem25"
          type="radio"
          name="barPieRadioGroup"
          value="81.25"
          hidden="hidden"
        />
        <label
          for="p1_barPieItem25"
          class="barPie__ring__item"
        >
          <div></div>
        </label>
        <input
          id="p1_barPieItem24"
          type="radio"
          name="barPieRadioGroup"
          value="78.125"
          hidden="hidden"
        />
        <label
          for="p1_barPieItem24"
          class="barPie__ring__item"
        >
          <div></div>
        </label>
        <input
          id="p1_barPieItem23"
          type="radio"
          name="barPieRadioGroup"
          value="75"
          hidden="hidden"
        />
        <label
          for="p1_barPieItem23"
          class="barPie__ring__item"
        >
          <div></div>
        </label>
        <input
          id="p1_barPieItem22"
          type="radio"
          name="barPieRadioGroup"
          value="71.875"
          hidden="hidden"
        />
        <label
          for="p1_barPieItem22"
          class="barPie__ring__item"
        >
          <div></div>
        </label>
        <input
          id="p1_barPieItem21"
          type="radio"
          name="barPieRadioGroup"
          value="68.75"
          hidden="hidden"
        />
        <label
          for="p1_barPieItem21"
          class="barPie__ring__item"
        >
          <div></div>
        </label>
        <input
          id="p1_barPieItem20"
          type="radio"
          name="barPieRadioGroup"
          value="65.625"
          hidden="hidden"
        />
        <label
          for="p1_barPieItem20"
          class="barPie__ring__item"
        >
          <div></div>
        </label>
        <input
          id="p1_barPieItem19"
          type="radio"
          name="barPieRadioGroup"
          value="62.5"
          hidden="hidden"
        />
        <label
          for="p1_barPieItem19"
          class="barPie__ring__item"
        >
          <div></div>
        </label>
        <input
          id="p1_barPieItem18"
          type="radio"
          name="barPieRadioGroup"
          value="59.375"
          hidden="hidden"
        />
        <label
          for="p1_barPieItem18"
          class="barPie__ring__item"
        >
          <div></div>
        </label>
        <input
          id="p1_barPieItem17"
          type="radio"
          name="barPieRadioGroup"
          value="56.25"
          hidden="hidden"
        />
        <label
          for="p1_barPieItem17"
          class="barPie__ring__item"
        >
          <div></div>
        </label>
        <input
          id="p1_barPieItem16"
          type="radio"
          name="barPieRadioGroup"
          value="53.125"
          hidden="hidden"
        />
        <label
          for="p1_barPieItem16"
          class="barPie__ring__item"
        >
          <div></div>
        </label>
        <input
          id="p1_barPieItem15"
          type="radio"
          name="barPieRadioGroup"
          value="50"
          hidden="hidden"
        />
        <label
          for="p1_barPieItem15"
          class="barPie__ring__item"
        >
          <div></div>
        </label>
        <input
          id="p1_barPieItem14"
          type="radio"
          name="barPieRadioGroup"
          value="46.875"
          hidden="hidden"
        />
        <label
          for="p1_barPieItem14"
          class="barPie__ring__item"
        >
          <div></div>
        </label>
        <input
          id="p1_barPieItem13"
          type="radio"
          name="barPieRadioGroup"
          value="43.75"
          hidden="hidden"
        />
        <label
          for="p1_barPieItem13"
          class="barPie__ring__item"
        >
          <div></div>
        </label>
        <input
          id="p1_barPieItem12"
          type="radio"
          name="barPieRadioGroup"
          value="40.625"
          hidden="hidden"
        />
        <label
          for="p1_barPieItem12"
          class="barPie__ring__item"
        >
          <div></div>
        </label>
        <input
          id="p1_barPieItem11"
          type="radio"
          name="barPieRadioGroup"
          value="37.5"
          hidden="hidden"
        />
        <label
          for="p1_barPieItem11"
          class="barPie__ring__item"
        >
          <div></div>
        </label>
        <input
          id="p1_barPieItem10"
          type="radio"
          name="barPieRadioGroup"
          value="34.375"
          hidden="hidden"
        />
        <label
          for="p1_barPieItem10"
          class="barPie__ring__item"
        >
          <div></div>
        </label>
        <input
          id="p1_barPieItem9"
          type="radio"
          name="barPieRadioGroup"
          value="31.25"
          hidden="hidden"
        />
        <label
          for="p1_barPieItem9"
          class="barPie__ring__item"
        >
          <div></div>
        </label>
        <input
          id="p1_barPieItem8"
          type="radio"
          name="barPieRadioGroup"
          value="28.125"
          hidden="hidden"
        />
        <label
          for="p1_barPieItem8"
          class="barPie__ring__item"
        >
          <div></div>
        </label>
        <input
          id="p1_barPieItem7"
          type="radio"
          name="barPieRadioGroup"
          value="25"
          hidden="hidden"
        />
        <label
          for="p1_barPieItem7"
          class="barPie__ring__item"
        >
          <div></div>
        </label>
        <input
          id="p1_barPieItem6"
          type="radio"
          name="barPieRadioGroup"
          value="21.875"
          hidden="hidden"
        />
        <label
          for="p1_barPieItem6"
          class="barPie__ring__item"
        >
          <div></div>
        </label>
        <input
          id="p1_barPieItem5"
          type="radio"
          name="barPieRadioGroup"
          value="18.75"
          hidden="hidden"
        />
        <label
          for="p1_barPieItem5"
          class="barPie__ring__item"
        >
          <div></div>
        </label>
        <input
          id="p1_barPieItem4"
          type="radio"
          name="barPieRadioGroup"
          value="15.625"
          hidden="hidden"
        />
        <label
          for="p1_barPieItem4"
          class="barPie__ring__item"
        >
          <div></div>
        </label>
        <input
          id="p1_barPieItem3"
          type="radio"
          name="barPieRadioGroup"
          value="12.5"
          hidden="hidden"
        />
        <label
          for="p1_barPieItem3"
          class="barPie__ring__item"
        >
          <div></div>
        </label>
        <input
          id="p1_barPieItem2"
          type="radio"
          name="barPieRadioGroup"
          value="9.375"
          hidden="hidden"
        />
        <label
          for="p1_barPieItem2"
          class="barPie__ring__item"
        >
          <div></div>
        </label>
        <input
          id="p1_barPieItem1"
          type="radio"
          name="barPieRadioGroup"
          value="6.25"
          hidden="hidden"
        />
        <label
          for="p1_barPieItem1"
          class="barPie__ring__item"
        >
          <div></div>
        </label>
        <input
          id="p1_barPieItem0"
          type="radio"
          name="barPieRadioGroup"
          value="3.125"
          hidden="hidden"
        />
        <label
          for="p1_barPieItem0"
          class="barPie__ring__item"
        >
          <div></div>
        </label>
      </div>
      <div class="circleProgress_wrapper">
        <div class="wrapper right"></div>
        <div class="wrapper left"></div>
      </div>
    </div>
  </div>
</template>

<script>
import $ from 'jquery'

/* eslint-disable */
$(function () {
  clearTimeout(timer)
  function rgbToHex(r, g, b) {
    var hex = ((r << 16) | (g << 8) | b).toString(16)
    return '#' + new Array(Math.abs(hex.length - 7)).join('0') + hex
  }

  // hex to rgb
  function hexToRgb(hex) {
    var rgb = []
    for (var i = 1; i < 7; i += 2) {
      rgb.push(parseInt('0x' + hex.slice(i, i + 2)))
    }
    return rgb
  }

  // 计算渐变过渡色
  function gradient(startColor, endColor, step) {
    // 将 hex 转换为rgb
    var sColor = hexToRgb(startColor),
      eColor = hexToRgb(endColor)

    // 计算R\G\B每一步的差值
    var rStep = (eColor[0] - sColor[0]) / step
    var gStep = (eColor[1] - sColor[1]) / step
    var bStep = (eColor[2] - sColor[2]) / step

    var gradientColorArr = []
    for (var i = 0; i < step; i++) {
      // 计算每一步的hex值
      gradientColorArr.push(
        rgbToHex(
          parseInt(rStep * i + sColor[0]),
          parseInt(gStep * i + sColor[1]),
          parseInt(bStep * i + sColor[2])
        )
      )
    }
    return gradientColorArr
  }

  var startColor = '#FD827C'
  var endColor = '#FD827C'
  var colorArr = gradient(startColor, endColor, 32)

  // 内圆圈动画
  function percent(percent) {
    if (percent <= 50) {
      var xz = 45 + 3.6 * percent
      $('.rightcircle').css('transform', 'rotate(' + xz + 'deg)')
    } else if (percent <= 100) {
      var xz = 45 + 3.6 * (percent - 50)
      $('.rightcircle').css('transform', 'rotate(225deg)')
      $('.leftcircle').css('transform', 'rotate(' + xz + 'deg)')
    }
  }

  // 添加定时器
  var timer = setTimeout(yundong, 500)
  //对2秒计数
  var js = 1
  //当前是百分之几
  var baifenbi = 0
  var baifenbij = 0
  function yundong() {
    baifenbij = baifenbi
    if (js <= 2) {
      baifenbi = baifenbi + 20
      percent(baifenbi)
      shuzi()
    } else if (js <= 3) {
      baifenbi = baifenbi + 10
      percent(baifenbi)
      shuzi()
    } else if (js <= 13) {
      baifenbi = baifenbi + 2
      percent(baifenbi)
      shuzi()
    } else if (js <= 33) {
      baifenbi = baifenbi + 1
      percent(baifenbi)
      shuzi()
    }
    js++
  }

  function shuzi() {
    $({
      // 起始值
      countNum: baifenbij
    }).animate(
      {
        // 最终值
        countNum: baifenbi
      },
      {
        // 动画持续时间
        duration: 2000,
        easing: 'linear',
        step: function () {
          // 设置每步动画计算的数值
          $('.barPie__value').text(Math.round((this.countNum * 100) / 100))
          var djge = Math.floor(this.countNum / 3.125)
          $("[for='p1_barPieItem" + djge + "']")
            .find('div')
            .addClass('gaoliang')
          $("[for='p1_barPieItem" + djge + "']")
            .find('div')
            .css('background', colorArr[djge])
        },
        complete: function () {
          // 设置动画结束的数值
          $('.barPie__value').text(this.countNum)
        }
      }
    )
  }
})
export default {}
</script>

<style lang="scss">
@import url('./style.css');
</style>
